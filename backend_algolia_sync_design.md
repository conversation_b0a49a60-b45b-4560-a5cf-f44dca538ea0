# 后端 Algolia 数据同步设计方案

## 概述

本文档描述了如何在后端实现 Algolia 数据同步机制，以支持前端的高性能搜索功能。

## 架构设计

### 1. 同步策略

#### 实时同步
- **触发时机**: 数据库 CRUD 操作后
- **实现方式**: 使用 Spring 事件机制或 AOP
- **适用场景**: 关键数据更新（动态发布、用户信息变更等）

#### 批量同步
- **触发时机**: 定时任务（每小时/每天）
- **实现方式**: Spring Scheduler + 分页处理
- **适用场景**: 大量数据初始化、数据修复

#### 增量同步
- **触发时机**: 定时任务（每15分钟）
- **实现方式**: 基于时间戳的增量查询
- **适用场景**: 日常数据同步

### 2. 索引结构设计

#### Moments 索引 (moments)
```json
{
  "objectID": "moment_123",
  "id": 123,
  "title": "钓鱼心得分享",
  "content": "今天在某某湖钓到了大鲫鱼...",
  "authorId": 456,
  "authorName": "钓鱼达人",
  "authorAvatar": "https://...",
  "momentType": "FISHING_SHARE",
  "tags": ["鲫鱼", "野钓", "技巧"],
  "location": "北京市朝阳区",
  "province": "北京市",
  "city": "北京市",
  "county": "朝阳区",
  "fishingSpotId": 789,
  "fishingSpotName": "某某湖",
  "likeCount": 25,
  "commentCount": 8,
  "visibility": "PUBLIC",
  "createdAt": 1703123456,
  "updatedAt": 1703123456,
  "_geoloc": {
    "lat": 39.9042,
    "lng": 116.4074
  }
}
```

#### Users 索引 (users)
```json
{
  "objectID": "user_456",
  "id": 456,
  "name": "钓鱼达人",
  "bio": "专业钓鱼20年，分享钓鱼技巧",
  "avatarUrl": "https://...",
  "level": "EXPERT",
  "location": "北京市朝阳区",
  "province": "北京市",
  "city": "北京市",
  "county": "朝阳区",
  "followersCount": 1200,
  "followingCount": 300,
  "momentsCount": 150,
  "tags": ["野钓", "路亚", "海钓"],
  "createdAt": 1703123456,
  "isActive": true
}
```

#### Fishing Spots 索引 (fishing_spots)
```json
{
  "objectID": "spot_789",
  "id": 789,
  "name": "某某湖",
  "description": "风景优美的天然湖泊，鱼类丰富",
  "address": "北京市朝阳区某某路123号",
  "province": "北京市",
  "city": "北京市",
  "county": "朝阳区",
  "fishTypes": ["鲫鱼", "鲤鱼", "草鱼"],
  "facilities": ["停车场", "餐厅", "厕所"],
  "rating": 4.5,
  "reviewCount": 89,
  "priceRange": "FREE",
  "tags": ["野钓", "免费", "风景好"],
  "createdAt": 1703123456,
  "isActive": true,
  "_geoloc": {
    "lat": 39.9042,
    "lng": 116.4074
  }
}
```

### 3. 服务实现

#### AlgoliaIndexService
```java
@Service
@Slf4j
public class AlgoliaIndexService {
    
    private final SearchClient algoliaClient;
    private final SearchIndex momentsIndex;
    private final SearchIndex usersIndex;
    private final SearchIndex spotsIndex;
    
    @Autowired
    public AlgoliaIndexService(AlgoliaConfig algoliaConfig) {
        this.algoliaClient = new SearchClient(
            algoliaConfig.getAppId(), 
            algoliaConfig.getApiKey()
        );
        this.momentsIndex = algoliaClient.initIndex("moments");
        this.usersIndex = algoliaClient.initIndex("users");
        this.spotsIndex = algoliaClient.initIndex("fishing_spots");
    }
    
    // 同步单个动态
    public void syncMoment(Moment moment) {
        try {
            MomentIndexData indexData = convertToIndexData(moment);
            momentsIndex.saveObject(indexData);
            log.info("同步动态到 Algolia 成功: {}", moment.getId());
        } catch (Exception e) {
            log.error("同步动态到 Algolia 失败: {}", moment.getId(), e);
        }
    }
    
    // 批量同步动态
    public void batchSyncMoments(List<Moment> moments) {
        try {
            List<MomentIndexData> indexDataList = moments.stream()
                .map(this::convertToIndexData)
                .collect(Collectors.toList());
            momentsIndex.saveObjects(indexDataList);
            log.info("批量同步 {} 个动态到 Algolia 成功", moments.size());
        } catch (Exception e) {
            log.error("批量同步动态到 Algolia 失败", e);
        }
    }
    
    // 删除动态索引
    public void deleteMoment(Long momentId) {
        try {
            momentsIndex.deleteObject("moment_" + momentId);
            log.info("从 Algolia 删除动态成功: {}", momentId);
        } catch (Exception e) {
            log.error("从 Algolia 删除动态失败: {}", momentId, e);
        }
    }
    
    private MomentIndexData convertToIndexData(Moment moment) {
        // 转换逻辑
        return MomentIndexData.builder()
            .objectID("moment_" + moment.getId())
            .id(moment.getId())
            .title(extractTitle(moment.getContent()))
            .content(moment.getContent())
            // ... 其他字段
            .build();
    }
}
```

#### 事件驱动同步
```java
@Component
@Slf4j
public class AlgoliaSyncEventListener {
    
    private final AlgoliaIndexService algoliaIndexService;
    
    @EventListener
    @Async
    public void handleMomentCreated(MomentCreatedEvent event) {
        algoliaIndexService.syncMoment(event.getMoment());
    }
    
    @EventListener
    @Async
    public void handleMomentUpdated(MomentUpdatedEvent event) {
        algoliaIndexService.syncMoment(event.getMoment());
    }
    
    @EventListener
    @Async
    public void handleMomentDeleted(MomentDeletedEvent event) {
        algoliaIndexService.deleteMoment(event.getMomentId());
    }
}
```

#### 定时同步任务
```java
@Component
@Slf4j
public class AlgoliaSyncScheduler {
    
    private final AlgoliaIndexService algoliaIndexService;
    private final MomentService momentService;
    
    // 增量同步 - 每15分钟执行
    @Scheduled(fixedRate = 15 * 60 * 1000)
    public void incrementalSync() {
        try {
            LocalDateTime lastSyncTime = getLastSyncTime();
            List<Moment> updatedMoments = momentService.findUpdatedSince(lastSyncTime);
            
            if (!updatedMoments.isEmpty()) {
                algoliaIndexService.batchSyncMoments(updatedMoments);
                updateLastSyncTime(LocalDateTime.now());
                log.info("增量同步完成，同步了 {} 个动态", updatedMoments.size());
            }
        } catch (Exception e) {
            log.error("增量同步失败", e);
        }
    }
    
    // 全量同步 - 每天凌晨2点执行
    @Scheduled(cron = "0 0 2 * * ?")
    public void fullSync() {
        try {
            log.info("开始全量同步到 Algolia");
            
            // 分页处理大量数据
            int pageSize = 1000;
            int page = 0;
            
            while (true) {
                List<Moment> moments = momentService.findAll(page, pageSize);
                if (moments.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncMoments(moments);
                page++;
                
                // 避免过度占用资源
                Thread.sleep(1000);
            }
            
            log.info("全量同步完成");
        } catch (Exception e) {
            log.error("全量同步失败", e);
        }
    }
}
```

### 4. 配置管理

#### application.yml
```yaml
algolia-config:
  app-id: ${ALGOLIA_APP_ID:YOUR_APP_ID}
  api-key: ${ALGOLIA_API_KEY:YOUR_API_KEY}
  
spring:
  task:
    execution:
      pool:
        core-size: 5
        max-size: 10
        queue-capacity: 100
```

### 5. 监控和错误处理

#### 同步状态监控
```java
@RestController
@RequestMapping("/admin/algolia")
public class AlgoliaSyncController {
    
    @GetMapping("/sync-status")
    public ResponseEntity<SyncStatus> getSyncStatus() {
        // 返回同步状态信息
    }
    
    @PostMapping("/manual-sync")
    public ResponseEntity<String> manualSync(@RequestParam String type) {
        // 手动触发同步
    }
    
    @PostMapping("/reindex")
    public ResponseEntity<String> reindex() {
        // 重建索引
    }
}
```

#### 错误重试机制
```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public void syncWithRetry(Object data) {
    // 带重试的同步逻辑
}
```

### 6. 性能优化

1. **批量操作**: 使用 Algolia 的批量 API 减少网络请求
2. **异步处理**: 使用 @Async 避免阻塞主线程
3. **分页处理**: 大量数据分批处理避免内存溢出
4. **缓存机制**: 缓存最近同步时间等状态信息
5. **限流控制**: 避免过于频繁的 API 调用

### 7. 部署建议

1. **环境变量**: 敏感信息通过环境变量配置
2. **监控告警**: 集成监控系统，同步失败时及时告警
3. **日志记录**: 详细记录同步过程，便于问题排查
4. **备份策略**: 定期备份 Algolia 索引数据

## 总结

通过这套完整的数据同步机制，可以确保：
- 前端搜索数据的实时性和准确性
- 系统的高可用性和容错能力
- 良好的性能和用户体验
- 便于维护和监控的架构设计
