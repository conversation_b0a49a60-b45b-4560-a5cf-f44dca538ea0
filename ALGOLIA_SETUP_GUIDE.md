# Algolia 搜索功能设置指南

## 概述

本指南将帮助您完成 Algolia 搜索功能的设置和配置，包括前端集成和后端数据同步。

## 前置条件

1. Algolia 账户和应用
2. Flutter 开发环境
3. 后端 Java Spring Boot 环境

## 第一步：Algolia 账户设置

### 1. 创建 Algolia 账户
1. 访问 [Algolia 官网](https://www.algolia.com/)
2. 注册账户并创建应用
3. 获取 Application ID 和 API Keys

### 2. 创建索引
在 Algolia Dashboard 中创建以下索引：
- `moments` - 动态内容索引
- `users` - 用户信息索引  
- `fishing_spots` - 钓点信息索引

### 3. 配置索引设置
为每个索引配置以下设置：

#### Moments 索引配置
```json
{
  "searchableAttributes": [
    "title",
    "content", 
    "authorName",
    "tags"
  ],
  "attributesForFaceting": [
    "province",
    "city", 
    "momentType",
    "tags"
  ],
  "customRanking": [
    "desc(likeCount)",
    "desc(commentCount)",
    "desc(createdAt)"
  ],
  "attributesToHighlight": [
    "title",
    "content",
    "authorName"
  ]
}
```

#### Users 索引配置
```json
{
  "searchableAttributes": [
    "name",
    "bio"
  ],
  "attributesForFaceting": [
    "province",
    "city",
    "level"
  ],
  "customRanking": [
    "desc(followersCount)",
    "desc(momentsCount)"
  ]
}
```

#### Fishing Spots 索引配置
```json
{
  "searchableAttributes": [
    "name",
    "description",
    "address"
  ],
  "attributesForFaceting": [
    "province", 
    "city",
    "fishTypes",
    "priceRange"
  ],
  "customRanking": [
    "desc(rating)",
    "desc(reviewCount)"
  ]
}
```

## 第二步：前端配置

### 1. 更新配置文件
编辑 `user_app/lib/config/algolia_config.dart`：

```dart
class AlgoliaConfig {
  // 替换为您的实际 Algolia 配置
  static const String appId = 'YOUR_ALGOLIA_APP_ID';
  static const String searchApiKey = 'YOUR_ALGOLIA_SEARCH_API_KEY';
  
  // 索引名称（确保与 Algolia Dashboard 中的名称一致）
  static const String momentsIndex = 'moments';
  static const String usersIndex = 'users';
  static const String fishingSpotsIndex = 'fishing_spots';
  
  // 其他配置保持不变...
}
```

### 2. 更新依赖注入配置
编辑 `user_app/lib/core/di/injection.dart`，确保 SearchClient 正确配置：

```dart
i.registerLazySingleton<SearchClient>(
  () => SearchClient(
    appId: AlgoliaConfig.appId,
    apiKey: AlgoliaConfig.searchApiKey,
  ),
);
```

### 3. 环境变量配置（推荐）
为了安全起见，建议使用环境变量：

创建 `.env` 文件：
```
ALGOLIA_APP_ID=your_app_id
ALGOLIA_SEARCH_API_KEY=your_search_api_key
```

然后在代码中使用：
```dart
static const String appId = String.fromEnvironment('ALGOLIA_APP_ID', defaultValue: 'YOUR_DEFAULT_APP_ID');
```

## 第三步：后端配置

### 1. 添加依赖
在 `pom.xml` 中添加 Algolia 依赖：

```xml
<dependency>
    <groupId>com.algolia</groupId>
    <artifactId>algoliasearch</artifactId>
    <version>4.5.4</version>
</dependency>
```

### 2. 配置文件
在 `application.yml` 中添加配置：

```yaml
algolia-config:
  app-id: ${ALGOLIA_APP_ID:your_app_id}
  api-key: ${ALGOLIA_API_KEY:your_api_key}
```

### 3. 创建配置类
```java
@Data
@Configuration
@ConfigurationProperties(prefix = "algolia-config")
public class AlgoliaConfig {
    private String appId;
    private String apiKey;

    @Bean
    public SearchClient algoliaClient() {
        return new SearchClient(appId, apiKey);
    }
}
```

### 4. 实现同步服务
参考 `backend_algolia_sync_design.md` 中的详细实现。

## 第四步：数据初始化

### 1. 准备数据
确保数据库中有测试数据：
- 动态内容
- 用户信息
- 钓点信息

### 2. 执行初始同步
运行后端的全量同步任务，将现有数据同步到 Algolia：

```java
@PostMapping("/admin/algolia/initial-sync")
public ResponseEntity<String> initialSync() {
    algoliaIndexService.fullSync();
    return ResponseEntity.ok("Initial sync started");
}
```

### 3. 验证数据
在 Algolia Dashboard 中检查索引是否包含正确的数据。

## 第五步：测试

### 1. 前端测试
1. 启动 Flutter 应用
2. 导航到搜索页面
3. 测试以下功能：
   - 基本搜索
   - 搜索建议
   - 分类筛选
   - 搜索历史

### 2. 后端测试
1. 创建新的动态/用户/钓点
2. 验证数据是否自动同步到 Algolia
3. 测试搜索结果是否包含新数据

### 3. 运行单元测试
```bash
cd user_app
flutter test test/services/algolia_search_service_test.dart
```

## 第六步：监控和维护

### 1. 设置监控
- 在 Algolia Dashboard 中监控搜索使用情况
- 设置告警通知
- 监控 API 调用量和成本

### 2. 性能优化
- 根据搜索分析优化索引配置
- 调整搜索参数
- 优化同步频率

### 3. 定期维护
- 检查数据一致性
- 清理无效数据
- 更新索引配置

## 故障排除

### 常见问题

#### 1. 搜索无结果
- 检查 API Key 是否正确
- 验证索引名称是否匹配
- 确认数据已同步到 Algolia

#### 2. 同步失败
- 检查网络连接
- 验证 API Key 权限
- 查看错误日志

#### 3. 搜索性能问题
- 检查索引配置
- 优化搜索参数
- 考虑使用缓存

### 调试技巧

1. **启用详细日志**：
```dart
debugPrint('Algolia search request: $searchRequest');
```

2. **使用 Algolia Dashboard**：
在 Dashboard 中查看搜索查询和结果

3. **网络抓包**：
使用开发者工具查看 API 请求

## 安全注意事项

1. **API Key 管理**：
   - 使用只读的 Search API Key
   - 不要在客户端暴露 Admin API Key
   - 定期轮换 API Key

2. **数据安全**：
   - 确保敏感数据不被索引
   - 使用适当的访问控制
   - 定期审查索引内容

3. **成本控制**：
   - 监控 API 使用量
   - 设置使用限制
   - 优化搜索频率

## 总结

完成以上步骤后，您的应用将拥有强大的 Algolia 搜索功能。记住定期监控和优化搜索性能，以提供最佳的用户体验。

如有问题，请参考：
- [Algolia 官方文档](https://www.algolia.com/doc/)
- [Flutter Algolia 包文档](https://pub.dev/packages/algoliasearch)
- 项目中的 `search_implementation_summary.md`
