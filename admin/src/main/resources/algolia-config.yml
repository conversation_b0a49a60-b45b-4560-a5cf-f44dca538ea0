# Algolia 搜索配置
algolia-config:
  app-id: ${ALGOLIA_APP_ID:LE1PW7ZSUA}
  api-key: ${ALGOLIA_API_KEY:********************************}

# Algolia 同步配置
algolia:
  sync:
    # 是否启用同步
    enabled: ${ALGOLIA_SYNC_ENABLED:true}
    # 批量同步大小
    batch-size: ${ALGOLIA_BATCH_SIZE:1000}
    # 重试次数
    retry-attempts: ${ALGOLIA_RETRY_ATTEMPTS:3}
    # 重试延迟（毫秒）
    retry-delay: ${ALGOLIA_RETRY_DELAY:1000}
    # 索引名称配置
    indexes:
      moments: ${ALGOLIA_MOMENTS_INDEX:moments}
      users: ${ALGOLIA_USERS_INDEX:users}
      fishing-spots: ${ALGOLIA_FISHING_SPOTS_INDEX:fishing_spots}

# Spring 任务执行配置
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 5
        # 最大线程数
        max-size: 10
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: "algolia-"
        # 线程空闲时间
        keep-alive: 60s
    scheduling:
      pool:
        # 调度线程池大小
        size: 2
