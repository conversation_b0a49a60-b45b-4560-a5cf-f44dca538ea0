package com.fishing.web.controller;

import com.fishing.service.AlgoliaIndexService;
import com.fishing.scheduler.AlgoliaSyncScheduler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Algolia 同步管理控制器
 */
@RestController
@RequestMapping("/admin/algolia")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AlgoliaSyncController {
    
    private final AlgoliaIndexService algoliaIndexService;
    private final AlgoliaSyncScheduler algoliaSyncScheduler;
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final String LAST_SYNC_TIME_KEY = "algolia:last_sync_time";
    
    /**
     * 获取同步状态
     */
    @GetMapping("/sync-status")
    public ResponseEntity<Map<String, Object>> getSyncStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 获取最后同步时间
            String lastSyncTimeStr = redisTemplate.opsForValue().get(LAST_SYNC_TIME_KEY);
            if (lastSyncTimeStr != null) {
                LocalDateTime lastSyncTime = LocalDateTime.parse(lastSyncTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                status.put("lastSyncTime", lastSyncTime);
                
                // 计算距离上次同步的时间
                long minutesSinceLastSync = java.time.Duration.between(lastSyncTime, LocalDateTime.now()).toMinutes();
                status.put("minutesSinceLastSync", minutesSinceLastSync);
                status.put("syncStatus", minutesSinceLastSync < 30 ? "HEALTHY" : "DELAYED");
            } else {
                status.put("syncStatus", "UNKNOWN");
            }
            
            // 检查同步锁状态
            boolean incrementalLocked = Boolean.TRUE.equals(redisTemplate.hasKey("algolia:sync_lock:incremental"));
            boolean fullLocked = Boolean.TRUE.equals(redisTemplate.hasKey("algolia:sync_lock:full"));
            
            status.put("incrementalSyncRunning", incrementalLocked);
            status.put("fullSyncRunning", fullLocked);
            status.put("currentTime", LocalDateTime.now());
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "获取同步状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 手动触发增量同步
     */
    @PostMapping("/manual-sync")
    public ResponseEntity<Map<String, String>> manualSync(@RequestParam(defaultValue = "incremental") String type) {
        try {
            CompletableFuture<Void> future;
            
            switch (type.toLowerCase()) {
                case "incremental":
                    future = CompletableFuture.runAsync(() -> algoliaSyncScheduler.incrementalSync());
                    break;
                case "full":
                    future = CompletableFuture.runAsync(() -> algoliaSyncScheduler.fullSync());
                    break;
                default:
                    return ResponseEntity.badRequest()
                        .body(Map.of("error", "不支持的同步类型: " + type));
            }
            
            log.info("手动触发 {} 同步", type);
            return ResponseEntity.ok(Map.of(
                "message", "同步任务已启动",
                "type", type,
                "startTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("手动触发同步失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "触发同步失败: " + e.getMessage()));
        }
    }
    
    /**
     * 重建索引
     */
    @PostMapping("/reindex")
    public ResponseEntity<Map<String, String>> reindex() {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始重建 Algolia 索引");
                    
                    // 清空现有索引
                    algoliaIndexService.clearAllIndexes();
                    
                    // 触发全量同步
                    algoliaSyncScheduler.fullSync();
                    
                    log.info("重建 Algolia 索引完成");
                } catch (Exception e) {
                    log.error("重建索引失败", e);
                }
            });
            
            return ResponseEntity.ok(Map.of(
                "message", "索引重建任务已启动",
                "startTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("重建索引失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "重建索引失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清空所有索引
     */
    @DeleteMapping("/clear-indexes")
    public ResponseEntity<Map<String, String>> clearIndexes() {
        try {
            algoliaIndexService.clearAllIndexes();
            
            log.info("清空所有 Algolia 索引");
            return ResponseEntity.ok(Map.of(
                "message", "所有索引已清空",
                "clearTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("清空索引失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "清空索引失败: " + e.getMessage()));
        }
    }
    
    /**
     * 同步指定动态
     */
    @PostMapping("/sync-moment/{momentId}")
    public ResponseEntity<Map<String, String>> syncMoment(@PathVariable Long momentId) {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    // 这里需要注入 MomentService 来获取动态
                    // Moment moment = momentService.getById(momentId);
                    // if (moment != null) {
                    //     algoliaIndexService.syncMoment(moment);
                    // }
                    log.info("同步动态 {} 到 Algolia", momentId);
                } catch (Exception e) {
                    log.error("同步动态失败: {}", momentId, e);
                }
            });
            
            return ResponseEntity.ok(Map.of(
                "message", "动态同步任务已启动",
                "momentId", momentId.toString(),
                "startTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("同步动态失败: {}", momentId, e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "同步动态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 同步指定用户
     */
    @PostMapping("/sync-user/{userId}")
    public ResponseEntity<Map<String, String>> syncUser(@PathVariable Long userId) {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    // 这里需要注入 UserService 来获取用户
                    // User user = userService.getById(userId);
                    // if (user != null) {
                    //     algoliaIndexService.syncUser(user);
                    // }
                    log.info("同步用户 {} 到 Algolia", userId);
                } catch (Exception e) {
                    log.error("同步用户失败: {}", userId, e);
                }
            });
            
            return ResponseEntity.ok(Map.of(
                "message", "用户同步任务已启动",
                "userId", userId.toString(),
                "startTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("同步用户失败: {}", userId, e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "同步用户失败: " + e.getMessage()));
        }
    }
    
    /**
     * 同步指定钓点
     */
    @PostMapping("/sync-spot/{spotId}")
    public ResponseEntity<Map<String, String>> syncSpot(@PathVariable Long spotId) {
        try {
            CompletableFuture.runAsync(() -> {
                try {
                    // 这里需要注入 FishingSpotService 来获取钓点
                    // FishingSpot spot = fishingSpotService.getById(spotId);
                    // if (spot != null) {
                    //     algoliaIndexService.syncFishingSpot(spot);
                    // }
                    log.info("同步钓点 {} 到 Algolia", spotId);
                } catch (Exception e) {
                    log.error("同步钓点失败: {}", spotId, e);
                }
            });
            
            return ResponseEntity.ok(Map.of(
                "message", "钓点同步任务已启动",
                "spotId", spotId.toString(),
                "startTime", LocalDateTime.now().toString()
            ));
            
        } catch (Exception e) {
            log.error("同步钓点失败: {}", spotId, e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "同步钓点失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取同步统计信息
     */
    @GetMapping("/sync-stats")
    public ResponseEntity<Map<String, Object>> getSyncStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // TODO: 实现统计信息收集
            // - 今日同步次数
            // - 同步成功率
            // - 平均同步时间
            // - 错误统计
            
            stats.put("message", "统计功能待实现");
            stats.put("queryTime", LocalDateTime.now());
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("获取同步统计失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "获取统计失败: " + e.getMessage()));
        }
    }
}
