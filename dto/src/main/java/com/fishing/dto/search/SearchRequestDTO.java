package com.fishing.dto.search;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;

/**
 * 搜索请求DTO
 */
@Data
public class SearchRequestDTO {
    
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 搜索类型：all(全部), moment(动态), user(用户), spot(钓点)
     */
    private String type = "all";
    
    /**
     * 排序方式：relevance(相关性), time(时间), hot(热度)
     */
    private String sortBy = "relevance";
    
    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页数量必须大于0")
    private Integer size = 10;
    
    /**
     * 省份筛选
     */
    private String province;
    
    /**
     * 城市筛选
     */
    private String city;
    
    /**
     * 时间范围筛选（天数）
     */
    private Integer dayRange;
}
