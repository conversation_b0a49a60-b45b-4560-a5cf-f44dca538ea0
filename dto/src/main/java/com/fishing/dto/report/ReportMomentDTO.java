package com.fishing.dto.report;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 举报动态DTO
 */
@Data
public class ReportMomentDTO {

    @NotNull(message = "动态ID不能为空")
    private Long momentId;

    @NotNull(message = "举报原因不能为空")
    @Size(min = 1, max = 500, message = "举报原因长度必须在1-500字符之间")
    private String reason;

    @NotNull(message = "举报类型不能为空")
    private String type;
}
