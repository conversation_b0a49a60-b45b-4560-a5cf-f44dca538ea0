package com.fishing.dto.algolia;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Algolia 动态索引数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MomentIndexData {
    
    /**
     * Algolia 对象ID，格式：moment_{id}
     */
    @JsonProperty("objectID")
    private String objectID;
    
    /**
     * 动态ID
     */
    private Long id;
    
    /**
     * 标题（从内容中提取）
     */
    private String title;
    
    /**
     * 动态内容
     */
    private String content;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者名称
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 动态类型
     */
    private String momentType;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 位置信息
     */
    private String location;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String county;
    
    /**
     * 钓点ID
     */
    private Long fishingSpotId;
    
    /**
     * 钓点名称
     */
    private String fishingSpotName;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论数
     */
    private Integer commentCount;
    
    /**
     * 可见性
     */
    private String visibility;
    
    /**
     * 创建时间（Unix 时间戳）
     */
    private Long createdAt;
    
    /**
     * 更新时间（Unix 时间戳）
     */
    private Long updatedAt;
    
    /**
     * 地理位置信息（Algolia 地理搜索格式）
     */
    @JsonProperty("_geoloc")
    private Map<String, BigDecimal> geoloc;
    
    /**
     * 图片列表
     */
    private List<String> images;
    
    /**
     * 封面图片
     */
    private String coverImage;
    
    /**
     * 类型特定数据
     */
    private Map<String, Object> typeSpecificData;
    
    /**
     * 是否活跃（用于过滤）
     */
    private Boolean isActive;
    
    /**
     * 热度分数（用于排序）
     */
    private Double hotScore;
    
    /**
     * 创建地理位置对象
     */
    public static Map<String, BigDecimal> createGeoloc(BigDecimal latitude, BigDecimal longitude) {
        if (latitude == null || longitude == null) {
            return null;
        }
        return Map.of(
            "lat", latitude,
            "lng", longitude
        );
    }
    
    /**
     * 从内容中提取标题
     */
    public static String extractTitle(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        String[] lines = content.split("\n");
        String firstLine = lines[0].trim();
        
        // 如果第一行太长，截取前50个字符
        if (firstLine.length() > 50) {
            return firstLine.substring(0, 50) + "...";
        }
        
        return firstLine;
    }
    
    /**
     * 计算热度分数
     */
    public static Double calculateHotScore(Integer likeCount, Integer commentCount, LocalDateTime createdAt) {
        if (likeCount == null) likeCount = 0;
        if (commentCount == null) commentCount = 0;
        
        // 基础分数：点赞数 * 1 + 评论数 * 2
        double baseScore = likeCount * 1.0 + commentCount * 2.0;
        
        // 时间衰减：越新的内容分数越高
        if (createdAt != null) {
            long daysSinceCreated = java.time.Duration.between(createdAt, LocalDateTime.now()).toDays();
            double timeDecay = Math.max(0.1, 1.0 - (daysSinceCreated * 0.01)); // 每天衰减1%
            baseScore *= timeDecay;
        }
        
        return baseScore;
    }
}
