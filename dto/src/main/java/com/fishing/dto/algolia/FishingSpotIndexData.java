package com.fishing.dto.algolia;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Algolia 钓点索引数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FishingSpotIndexData {
    
    /**
     * Algolia 对象ID，格式：spot_{id}
     */
    @JsonProperty("objectID")
    private String objectID;
    
    /**
     * 钓点ID
     */
    private Long id;
    
    /**
     * 钓点名称
     */
    private String name;
    
    /**
     * 钓点描述
     */
    private String description;
    
    /**
     * 详细地址
     */
    private String address;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String county;
    
    /**
     * 鱼类列表
     */
    private List<String> fishTypes;
    
    /**
     * 设施列表
     */
    private List<String> facilities;
    
    /**
     * 评分
     */
    private BigDecimal rating;
    
    /**
     * 评价数量
     */
    private Integer reviewCount;
    
    /**
     * 价格范围
     */
    private String priceRange;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 创建时间（Unix 时间戳）
     */
    private Long createdAt;
    
    /**
     * 是否活跃
     */
    private Boolean isActive;
    
    /**
     * 是否官方认证
     */
    private Boolean isOfficial;
    
    /**
     * 认证等级
     */
    private Integer verificationLevel;
    
    /**
     * 访问次数
     */
    private Integer visitorCount;
    
    /**
     * 签到次数
     */
    private Integer checkinCount;
    
    /**
     * 地理位置信息（Algolia 地理搜索格式）
     */
    @JsonProperty("_geoloc")
    private Map<String, BigDecimal> geoloc;
    
    /**
     * 是否付费
     */
    private Boolean isPaid;
    
    /**
     * 是否有设施
     */
    private Boolean hasFacilities;
    
    /**
     * 热度分数
     */
    private Double popularityScore;
    
    /**
     * 创建地理位置对象
     */
    public static Map<String, BigDecimal> createGeoloc(BigDecimal latitude, BigDecimal longitude) {
        if (latitude == null || longitude == null) {
            return null;
        }
        return Map.of(
            "lat", latitude,
            "lng", longitude
        );
    }
    
    /**
     * 解析鱼类列表
     */
    public static List<String> parseFishTypes(String fishTypesStr, String extraFishTypes) {
        List<String> fishTypes = new java.util.ArrayList<>();
        
        // 解析系统鱼类
        if (fishTypesStr != null && !fishTypesStr.trim().isEmpty()) {
            String[] types = fishTypesStr.split(",");
            for (String type : types) {
                String trimmed = type.trim();
                if (!trimmed.isEmpty()) {
                    fishTypes.add(trimmed);
                }
            }
        }
        
        // 解析用户自定义鱼类
        if (extraFishTypes != null && !extraFishTypes.trim().isEmpty()) {
            String[] types = extraFishTypes.split(",");
            for (String type : types) {
                String trimmed = type.trim();
                if (!trimmed.isEmpty() && !fishTypes.contains(trimmed)) {
                    fishTypes.add(trimmed);
                }
            }
        }
        
        return fishTypes;
    }
    
    /**
     * 解析设施列表
     */
    public static List<String> parseFacilities(String facilitiesStr, String extraFacilities) {
        List<String> facilities = new java.util.ArrayList<>();
        
        // 解析系统设施
        if (facilitiesStr != null && !facilitiesStr.trim().isEmpty()) {
            String[] types = facilitiesStr.split(",");
            for (String type : types) {
                String trimmed = type.trim();
                if (!trimmed.isEmpty()) {
                    facilities.add(trimmed);
                }
            }
        }
        
        // 解析用户自定义设施
        if (extraFacilities != null && !extraFacilities.trim().isEmpty()) {
            String[] types = extraFacilities.split(",");
            for (String type : types) {
                String trimmed = type.trim();
                if (!trimmed.isEmpty() && !facilities.contains(trimmed)) {
                    facilities.add(trimmed);
                }
            }
        }
        
        return facilities;
    }
    
    /**
     * 计算热度分数
     */
    public static Double calculatePopularityScore(BigDecimal rating, Integer reviewCount, 
                                                 Integer visitorCount, Integer checkinCount,
                                                 Boolean isOfficial, Integer verificationLevel) {
        double score = 0.0;
        
        // 评分权重 (0-5分，权重40%)
        if (rating != null) {
            score += rating.doubleValue() * 8.0; // 最高40分
        }
        
        // 评价数量权重 (权重20%)
        if (reviewCount != null) {
            score += Math.min(reviewCount * 0.5, 20.0); // 最高20分
        }
        
        // 访问次数权重 (权重20%)
        if (visitorCount != null) {
            score += Math.min(visitorCount * 0.01, 20.0); // 最高20分
        }
        
        // 签到次数权重 (权重10%)
        if (checkinCount != null) {
            score += Math.min(checkinCount * 0.1, 10.0); // 最高10分
        }
        
        // 官方认证加成 (权重10%)
        if (Boolean.TRUE.equals(isOfficial)) {
            score += 5.0;
            if (verificationLevel != null) {
                score += verificationLevel * 1.25; // 最高5分
            }
        }
        
        return score;
    }
    
    /**
     * 确定价格范围
     */
    public static String determinePriceRange(Boolean isPaid) {
        return Boolean.TRUE.equals(isPaid) ? "PAID" : "FREE";
    }
}
