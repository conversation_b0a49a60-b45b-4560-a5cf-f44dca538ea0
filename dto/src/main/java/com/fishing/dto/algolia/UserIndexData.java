package com.fishing.dto.algolia;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Algolia 用户索引数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserIndexData {
    
    /**
     * Algolia 对象ID，格式：user_{id}
     */
    @JsonProperty("objectID")
    private String objectID;
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String name;
    
    /**
     * 用户简介
     */
    private String bio;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 用户等级/头衔
     */
    private String level;
    
    /**
     * 位置信息
     */
    private String location;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String county;
    
    /**
     * 粉丝数
     */
    private Integer followersCount;
    
    /**
     * 关注数
     */
    private Integer followingCount;
    
    /**
     * 动态数
     */
    private Integer momentsCount;
    
    /**
     * 标签列表（兴趣、专长等）
     */
    private List<String> tags;
    
    /**
     * 创建时间（Unix 时间戳）
     */
    private Long createdAt;
    
    /**
     * 是否活跃
     */
    private Boolean isActive;
    
    /**
     * 用户活跃度分数
     */
    private Double activityScore;
    
    /**
     * 性别
     */
    private Integer gender;
    
    /**
     * 手机号（脱敏）
     */
    private String maskedPhone;
    
    /**
     * 计算用户活跃度分数
     */
    public static Double calculateActivityScore(Integer followersCount, Integer momentsCount, LocalDateTime createdAt) {
        if (followersCount == null) followersCount = 0;
        if (momentsCount == null) momentsCount = 0;
        
        // 基础分数：粉丝数 * 0.1 + 动态数 * 1
        double baseScore = followersCount * 0.1 + momentsCount * 1.0;
        
        // 注册时间加成：老用户有一定加成
        if (createdAt != null) {
            long daysSinceRegistered = java.time.Duration.between(createdAt, LocalDateTime.now()).toDays();
            double timeBonus = Math.min(1.5, 1.0 + (daysSinceRegistered * 0.001)); // 每天增加0.1%，最多50%
            baseScore *= timeBonus;
        }
        
        return baseScore;
    }
    
    /**
     * 脱敏手机号
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return null;
        }
        
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }
    
    /**
     * 构建位置信息
     */
    public static String buildLocation(String province, String city, String county) {
        StringBuilder location = new StringBuilder();
        
        if (province != null && !province.trim().isEmpty()) {
            location.append(province);
        }
        
        if (city != null && !city.trim().isEmpty()) {
            if (location.length() > 0) location.append(" ");
            location.append(city);
        }
        
        if (county != null && !county.trim().isEmpty()) {
            if (location.length() > 0) location.append(" ");
            location.append(county);
        }
        
        return location.length() > 0 ? location.toString() : null;
    }
}
