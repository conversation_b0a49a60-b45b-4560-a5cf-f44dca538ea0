<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fishing</groupId>
    <artifactId>fishing-backend</artifactId>
    <version>0.0.1</version>

    <name>fishing</name>
    <description>Fishing 管理系统</description>

    <properties>
        <fishing.version>0.0.1</fishing.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>2.0.34</fastjson.version>
        <oshi.version>6.4.3</oshi.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>5.2.3</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <aliyun-oss.version>3.16.1</aliyun-oss.version>
        <mp.version>3.5.7</mp.version>
        <spring-cloud.version>2025.0.0</spring-cloud.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>quartz</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>generator</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>framework</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>system</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>common</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>domain</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>service</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>oss</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>custom-exception</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>dto</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>vo</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>customer-service</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>config</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fishing</groupId>
                <artifactId>feign</artifactId>
                <version>${fishing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.algolia</groupId>
                <artifactId>algoliasearch</artifactId>
                <version>4.5.4</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.17.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.api.gateway</groupId>
                <artifactId>sdk-core-java</artifactId>
                <version>1.1.9</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>4.3.1</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>admin</module>
        <module>framework</module>
        <module>system</module>
        <module>quartz</module>
        <module>generator</module>
        <module>common</module>
        <module>user</module>
        <module>domain</module>
        <module>service</module>
        <module>oss</module>
        <module>custom-exception</module>
        <module>dto</module>
        <module>vo</module>
        <module>customer-service</module>
        <module>config</module>
        <module>feign</module>
    </modules>
    <packaging>pom</packaging>


    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>