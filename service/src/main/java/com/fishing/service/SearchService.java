package com.fishing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.dto.search.SearchRequestDTO;
import com.fishing.vo.search.SearchResultVO;
import com.fishing.vo.search.SearchSuggestionVO;

import java.util.List;

/**
 * 搜索服务接口
 */
public interface SearchService {
    
    /**
     * 综合搜索
     */
    Page<SearchResultVO> search(SearchRequestDTO request, Long userId);
    
    /**
     * 获取搜索建议
     */
    List<SearchSuggestionVO> getSearchSuggestions(String keyword, Integer limit, Long userId);
    
    /**
     * 获取搜索历史
     */
    List<String> getSearchHistory(Long userId);
    
    /**
     * 清除搜索历史
     */
    void clearSearchHistory(Long userId);
    
    /**
     * 获取热门搜索关键词
     */
    List<String> getHotSearchKeywords(Integer limit);
    
    /**
     * 记录搜索行为
     */
    void recordSearch(String keyword, Long userId);
}
