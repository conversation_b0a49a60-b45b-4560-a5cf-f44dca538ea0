package com.fishing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.User;
import com.fishing.vo.UserVo;

import java.time.LocalDateTime;
import java.util.List;

public interface IUserInfoService extends IService<User> {

    List<UserVo> getBy(List<Long> userIds);

    UserVo getBy(Long userId);

    UserVo updateUserInfo(User user);

    // ========== Algolia 同步相关方法 ==========

    /**
     * 获取用户粉丝数
     *
     * @param userId 用户ID
     * @return 粉丝数
     */
    Integer getFollowersCount(Long userId);

    /**
     * 获取用户关注数
     *
     * @param userId 用户ID
     * @return 关注数
     */
    Integer getFollowingCount(Long userId);

    /**
     * 查找指定时间之后更新的用户
     *
     * @param lastSyncTime 最后同步时间
     * @param page         页码
     * @param size         每页大小
     * @return 用户列表
     */
    List<User> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size);

    /**
     * 分页查找所有用户
     *
     * @param page 页码
     * @param size 每页大小
     * @return 用户列表
     */
    List<User> findAll(int page, int size);
}
