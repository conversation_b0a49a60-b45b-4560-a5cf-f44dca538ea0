package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.moment.MomentImage;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.mapper.*;
import com.fishing.service.BookmarkService;
import com.fishing.service.MomentCommentService;
import com.fishing.service.MomentLikeService;
import com.fishing.service.MomentService;
import com.fishing.vo.UserVo;
import com.fishing.vo.moment.MomentImageVO;
import com.fishing.vo.moment.MomentVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态服务实现类
 */
@Service
@RequiredArgsConstructor
public class MomentServiceImpl extends ServiceImpl<MomentMapper, Moment> implements MomentService {

    private final MomentImageMapper momentImageMapper;
    private final MomentDraftMapper momentDraftMapper;
    private final MomentLikeService momentLikeService;
    private final MomentCommentService momentCommentService;
    private final BookmarkService bookmarkService;
    private final UserMapper userMapper;
    private final FishingSpotMapper fishingSpotMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMoment(Moment moment, List<String> imageUrls) {
        moment.setCreatedAt(LocalDateTime.now());
        moment.setUpdatedAt(LocalDateTime.now());

        save(moment);

        if (CollectionUtils.isEmpty(imageUrls)) {
            return moment.getId();
        }

        saveImages(moment.getId(), imageUrls);
        return moment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMoment(Moment moment, List<String> imageUrls) {
        // 检查动态是否存在
        Moment existingMoment = getById(moment.getId());
        if (existingMoment == null) {
            return false;
        }

        // 检查权限
        if (!existingMoment.getUserId().equals(moment.getUserId())) {
            return false;
        }

        // 更新时间
        moment.setUpdatedAt(LocalDateTime.now());

        // 更新动态信息
        updateById(moment);

        // 删除原有图片
        LambdaQueryWrapper<MomentImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentImage::getMomentId, moment.getId());
        momentImageMapper.delete(wrapper);

        // 保存新图片
        if (imageUrls != null && !imageUrls.isEmpty()) {
            saveImages(moment.getId(), imageUrls);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMoment(Long momentId, Long userId) {
        // 检查动态是否存在
        Moment moment = getById(momentId);
        if (moment == null) {
            return false;
        }

        // 检查权限
        if (!moment.getUserId().equals(userId)) {
            return false;
        }

        // 删除动态（相关的图片、评论、点赞会通过外键级联删除）
        return removeById(momentId);
    }

    @Override
    public MomentVO getMomentDetail(Long momentId, Long currentUserId) {
        // 查询动态信息
        Moment moment = getById(momentId);
        if (moment == null) {
            return null;
        }

        return convertToMomentVO(moment, currentUserId);
    }

    @Override
    public Page<MomentVO> getUserMoments(Long userId, Page<Moment> page, Long currentUserId) {
        // 查询用户动态
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getUserId, userId)
                .orderByDesc(Moment::getCreatedAt);

        Page<Moment> momentPage = page(page, wrapper);

        return convertToMomentVOPage(momentPage, currentUserId);
    }

    @Override
    public Page<MomentVO> getFollowingMoments(Long userId, Page<Moment> page) {
        // 此处需要调用关注服务获取关注的用户列表，然后查询这些用户的动态
        // 由于关注服务未提供，此处仅做示例，实际实现需要根据项目情况修改

        Page<MomentVO> resultPage = new Page<>();
        resultPage.setTotal(0);
        resultPage.setSize(page.getSize());
        resultPage.setCurrent(page.getCurrent());
        resultPage.setRecords(new ArrayList<>());

        return resultPage;
    }

    @Override
    public Page<MomentVO> getRecommendedMoments(Page<Moment> page, Long currentUserId) {
        // 查询公开的动态，按时间排序
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getVisibility, "public")
                .orderByDesc(Moment::getCreatedAt);

        Page<Moment> momentPage = page(page, wrapper);

        return convertToMomentVOPage(momentPage, currentUserId);
    }

    @Override
    public Page<MomentVO> getMomentsByType(String momentType, Page<Moment> page, Long currentUserId) {
        // 查询指定类型的公开动态
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getMomentType, momentType)
                .eq(Moment::getVisibility, "public")
                .orderByDesc(Moment::getCreatedAt);

        Page<Moment> momentPage = page(page, wrapper);

        return convertToMomentVOPage(momentPage, currentUserId);
    }

    @Override
    public Page<MomentVO> getMomentsByFishingSpot(Long fishingSpotId, Page<Moment> page, Long currentUserId) {
        // 查询指定钓点的公开动态
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getFishingSpotId, fishingSpotId)
                .eq(Moment::getVisibility, "public")
                .orderByDesc(Moment::getCreatedAt);

        Page<Moment> momentPage = page(page, wrapper);

        return convertToMomentVOPage(momentPage, currentUserId);
    }

    @Override
    public Page<MomentVO> getMomentsWithFilters(Long filterUserId, String tag, String province, String city, String county, String momentType, Page<Moment> page, Long currentUserId) {
        // 构建基础查询条件
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();

        // 如果没有指定用户ID，则只查询公开动态；如果指定了用户ID，则查询该用户的所有动态
        if (filterUserId != null) {
            wrapper.eq(Moment::getUserId, filterUserId);
        } else {
            wrapper.eq(Moment::getVisibility, "public");
        }

        // 地理位置筛选：通过钓点的地理信息筛选
        if ((province != null && !province.trim().isEmpty()) ||
                (city != null && !city.trim().isEmpty()) ||
                (county != null && !county.trim().isEmpty())) {

            // 查询符合地理条件的钓点ID
            LambdaQueryWrapper<FishingSpot> spotWrapper = new LambdaQueryWrapper<>();
            if (province != null && !province.trim().isEmpty()) {
                spotWrapper.like(FishingSpot::getProvince, province);
            }
            if (city != null && !city.trim().isEmpty()) {
                spotWrapper.like(FishingSpot::getCity, city);
            }
            if (county != null && !county.trim().isEmpty()) {
                spotWrapper.like(FishingSpot::getCounty, county);
            }

            // 获取符合条件的钓点ID列表
            List<FishingSpot> spots = fishingSpotMapper.selectList(spotWrapper);
            if (spots.isEmpty()) {
                // 如果没有符合条件的钓点，返回空结果
                Page<Moment> emptyPage = new Page<>(page.getCurrent(), page.getSize());
                emptyPage.setTotal(0);
                emptyPage.setRecords(new ArrayList<>());
                return convertToMomentVOPage(emptyPage, currentUserId);
            }

            List<Long> spotIds = spots.stream().map(FishingSpot::getId).collect(Collectors.toList());
            wrapper.in(Moment::getFishingSpotId, spotIds);
        }

        // 标签筛选：TODO - 标签筛选功能待实现
        if (tag != null && !tag.trim().isEmpty()) {
            // 暂时跳过标签筛选，后续实现
        }

        // 动态类型筛选
        if (momentType != null && !momentType.trim().isEmpty()) {
            wrapper.eq(Moment::getMomentType, momentType);
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(Moment::getCreatedAt);

        Page<Moment> momentPage = page(page, wrapper);

        return convertToMomentVOPage(momentPage, currentUserId);
    }

    /**
     * 保存动态图片
     */
    private void saveImages(Long momentId, List<String> imageUrls) {
        for (int i = 0; i < imageUrls.size(); i++) {
            MomentImage image = new MomentImage();
            image.setMomentId(momentId);
            image.setImageUrl(imageUrls.get(i));
            image.setDisplayOrder(i);
            image.setCreatedAt(LocalDateTime.now());

            momentImageMapper.insert(image);
        }
    }

    /**
     * 将Moment对象转换为MomentVO对象
     */
    private MomentVO convertToMomentVO(Moment moment, Long currentUserId) {
        MomentVO vo = new MomentVO();
        BeanUtils.copyProperties(moment, vo);

        // 设置发布者信息
        if (moment.getUserId() != null) {
            User user = userMapper.selectById(moment.getUserId());
            if (user != null) {
                UserVo publisher = new UserVo();
                BeanUtils.copyProperties(user, publisher);
                vo.setPublisher(publisher);
            }
        }

        // 设置图片列表
        LambdaQueryWrapper<MomentImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentImage::getMomentId, moment.getId())
                .orderByAsc(MomentImage::getDisplayOrder);
        List<MomentImage> images = momentImageMapper.selectList(wrapper);

        List<MomentImageVO> imageVOs = images.stream().map(image -> {
            MomentImageVO imageVO = new MomentImageVO();
            BeanUtils.copyProperties(image, imageVO);
            return imageVO;
        }).collect(Collectors.toList());
        vo.setImages(imageVOs);

        // 设置点赞数和评论数
        vo.setLikeCount(momentLikeService.getLikeCount(moment.getId()));
        vo.setCommentCount(momentCommentService.getCommentCount(moment.getId()));

        // 设置当前用户是否点赞和收藏
        if (currentUserId != null) {
            vo.setIsLiked(momentLikeService.isLiked(moment.getId(), currentUserId));
            vo.setIsBookmarked(bookmarkService.isBookmarked(currentUserId, moment.getId()));
        } else {
            vo.setIsLiked(false);
            vo.setIsBookmarked(false);
        }

        return vo;
    }

    /**
     * 将Moment分页对象转换为MomentVO分页对象
     */
    private Page<MomentVO> convertToMomentVOPage(Page<Moment> page, Long currentUserId) {
        Page<MomentVO> resultPage = new Page<>();
        resultPage.setTotal(page.getTotal());
        resultPage.setSize(page.getSize());
        resultPage.setCurrent(page.getCurrent());

        List<Moment> records = page.getRecords();
        if (records.isEmpty()) {
            resultPage.setRecords(new ArrayList<>());
            return resultPage;
        }

        // 批量获取动态ID和用户ID
        List<Long> momentIds = records.stream()
                .map(Moment::getId)
                .collect(Collectors.toList());

        List<Long> userIds = records.stream()
                .map(Moment::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询用户信息
        Map<Long, User> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<User> users = userMapper.selectBatchIds(userIds);
            userMap = users.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));
        }

        // 批量查询图片
        LambdaQueryWrapper<MomentImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentImage::getMomentId, momentIds)
                .orderByAsc(MomentImage::getDisplayOrder);
        List<MomentImage> allImages = momentImageMapper.selectList(wrapper);

        // 按动态ID分组
        Map<Long, List<MomentImage>> imageMap = allImages.stream()
                .collect(Collectors.groupingBy(MomentImage::getMomentId));

        // 批量获取点赞状态和收藏状态
        List<Long> likedMomentIds = new ArrayList<>();
        List<Long> bookmarkedMomentIds = new ArrayList<>();
        if (currentUserId != null) {
            likedMomentIds = momentLikeService.batchGetLikeStatus(momentIds, currentUserId);
            bookmarkedMomentIds = bookmarkService.batchGetBookmarkStatus(currentUserId, momentIds);
        }

        // 批量获取评论数和点赞数
        List<Long> commentCounts = momentCommentService.batchGetCommentCounts(momentIds);
        Map<Long, Integer> likeCounts = momentLikeService.batchGetLikeCounts(momentIds);

        // 转换结果
        List<MomentVO> voList = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            Moment moment = records.get(i);
            MomentVO vo = new MomentVO();
            BeanUtils.copyProperties(moment, vo);

            // 设置发布者信息
            if (moment.getUserId() != null) {
                User user = userMap.get(moment.getUserId());
                if (user != null) {
                    UserVo publisher = new UserVo();
                    BeanUtils.copyProperties(user, publisher);
                    vo.setPublisher(publisher);
                }
            }

            // 设置图片
            List<MomentImage> images = imageMap.getOrDefault(moment.getId(), new ArrayList<>());
            List<MomentImageVO> imageVOs = images.stream().map(image -> {
                MomentImageVO imageVO = new MomentImageVO();
                BeanUtils.copyProperties(image, imageVO);
                return imageVO;
            }).collect(Collectors.toList());
            vo.setImages(imageVOs);

            // 设置点赞、收藏和评论数
            vo.setCommentCount(commentCounts.size() > i ? commentCounts.get(i).intValue() : 0);
            vo.setLikeCount(likeCounts.getOrDefault(moment.getId(), 0));

            // 设置当前用户是否点赞和收藏
            if (currentUserId != null) {
                vo.setIsLiked(likedMomentIds.contains(moment.getId()));
                vo.setIsBookmarked(bookmarkedMomentIds.contains(moment.getId()));
            } else {
                vo.setIsLiked(false);
                vo.setIsBookmarked(false);
            }

            voList.add(vo);
        }

        resultPage.setRecords(voList);
        return resultPage;
    }

    // ========== Algolia 同步相关方法实现 ==========

    @Override
    public Integer getLikeCount(Long momentId) {
        return momentLikeService.getLikeCount(momentId);
    }

    @Override
    public Integer getCommentCount(Long momentId) {
        return momentCommentService.getCommentCount(momentId);
    }

    @Override
    public Integer getMomentsCountByUserId(Long userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getUserId, userId);
        return Math.toIntExact(count(wrapper));
    }

    @Override
    public List<Moment> getMomentsByUserId(Long userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getUserId, userId)
                .orderByDesc(Moment::getCreatedAt);
        return list(wrapper);
    }

    @Override
    public List<Long> getMomentIdsByUserId(Long userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Moment::getId)
                .eq(Moment::getUserId, userId);
        return list(wrapper).stream()
                .map(Moment::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Moment> getMomentsBySpotId(Long spotId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getFishingSpotId, spotId)
                .orderByDesc(Moment::getCreatedAt);
        return list(wrapper);
    }

    @Override
    public List<Moment> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Moment::getUpdatedAt, lastSyncTime)
                .orderByAsc(Moment::getUpdatedAt);

        Page<Moment> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<Moment> result = page(pageParam, wrapper);

        return result.getRecords();
    }

    @Override
    public List<Moment> findAll(int page, int size) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(Moment::getId);

        Page<Moment> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<Moment> result = page(pageParam, wrapper);

        return result.getRecords();
    }
}