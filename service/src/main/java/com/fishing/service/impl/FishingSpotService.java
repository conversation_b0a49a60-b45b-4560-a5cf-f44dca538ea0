package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.custom.exception.BizException;
import com.fishing.domain.spot.*;
import com.fishing.dto.coordinate.CoordinateDto;
import com.fishing.dto.spot.QuerySpotDto;
import com.fishing.dto.spot.SpotCreateDto;
import com.fishing.mapper.*;
import com.fishing.service.*;
import com.fishing.vo.spot.SpotDetailVO;
import com.fishing.vo.spot.SpotFacilityVO;
import com.fishing.vo.spot.SpotPriceVO;
import com.fishing.vo.moment.MomentVO;
import com.fishing.vo.UserVo;
import com.fishing.mapper.UserMapper;
import com.fishing.mapper.MomentMapper;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;

/**
 * 钓点服务实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FishingSpotService extends ServiceImpl<FishingSpotMapper, FishingSpot> implements IFishingSpotService {

    private final SpotFishTypesMapper spotFishTypesMapper;
    private final SpotImageMapper spotImageMapper;
    private final SpotFishTypesService spotFishTypesService;
    private final FishTypeMapper fishTypeMapper;
    private final SpotCheckinService spotCheckinService;
    private final SpotPriceMapper spotPriceMapper;
    private final SpotFacilityMapper spotFacilityMapper;
    private final FacilityMapper facilityMapper;
    private final SpotCheckinMapper spotCheckinMapper;
    private final FacilityService facilityService;
    private final SpotFacilityService spotFacilityService;
    private final FishTypeService fishTypeService;
    private final SpotFavoriteMapper spotFavoriteMapper;
    private final SpotFavoriteService spotFavoriteService;
    private final UserMapper userMapper;
    private final MomentMapper momentMapper;

    public List<SpotDetailVO> queryList(FishingSpot fishingSpot) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();

        if (fishingSpot != null) {
            if (StringUtils.hasText(fishingSpot.getName())) {
                queryWrapper.like(FishingSpot::getName, fishingSpot.getName());
            }

            if (StringUtils.hasText(fishingSpot.getAddress())) {
                queryWrapper.like(FishingSpot::getAddress, fishingSpot.getAddress());
            }

            if (StringUtils.hasText(fishingSpot.getProvince())) {
                queryWrapper.eq(FishingSpot::getProvince, fishingSpot.getProvince());
            }
            if (StringUtils.hasText(fishingSpot.getCity())) {
                queryWrapper.eq(FishingSpot::getCity, fishingSpot.getCity());
            }
            if (StringUtils.hasText(fishingSpot.getCounty())) {
                queryWrapper.eq(FishingSpot::getCounty, fishingSpot.getCounty());
            }

            // 按状态查询
            if (fishingSpot.getStatus() != null) {
                queryWrapper.eq(FishingSpot::getStatus, fishingSpot.getStatus());
            }
        }

        queryWrapper.orderByDesc(FishingSpot::getCreatedAt);
        List<FishingSpot> fishingSpots = baseMapper.selectList(queryWrapper);

        return convertToSpotDetailVO(fishingSpots);
    }


    public Page<FishingSpot> queryNearbyPage(Page<FishingSpot> page, BigDecimal latitude, BigDecimal longitude, Double radius) {
        // 这里使用自定义Mapper方法进行地理位置查询
        return baseMapper.selectNearbyPage(page, latitude, longitude, radius);
    }


    public List<FishingSpot> findNearbySpots(BigDecimal latitude, BigDecimal longitude, Double radius) {
        return baseMapper.selectNearbySpots(latitude, longitude, radius);
    }


    public List<CoordinateDto> listCoordinates(FishingSpot fishingSpot) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件，只查询必要字段
        queryWrapper.select(FishingSpot::getId, FishingSpot::getName, FishingSpot::getLatitude, FishingSpot::getLongitude);

        // 应用筛选条件
        if (fishingSpot != null) {
            if (StringUtils.hasText(fishingSpot.getProvince())) {
                queryWrapper.eq(FishingSpot::getProvince, fishingSpot.getProvince());
            }
            if (StringUtils.hasText(fishingSpot.getCity())) {
                queryWrapper.eq(FishingSpot::getCity, fishingSpot.getCity());
            }
            if (StringUtils.hasText(fishingSpot.getCounty())) {
                queryWrapper.eq(FishingSpot::getCounty, fishingSpot.getCounty());
            }
            if (fishingSpot.getIsOfficial() != null) {
                queryWrapper.eq(FishingSpot::getIsOfficial, fishingSpot.getIsOfficial());
            }
        }

        // 查询数据并转换为DTO
        List<FishingSpot> fishingSpots = baseMapper.selectList(queryWrapper);
        return fishingSpots.stream()
                .map(spot -> new CoordinateDto(
                        spot.getId(),
                        spot.getLongitude().doubleValue(),
                        spot.getLatitude().doubleValue(),
                        spot.getName()
                ))
                .collect(Collectors.toList());
    }


    public boolean updateVerificationLevel(Long id, Integer level) {
        if (level < 0 || level > 3) {
            throw new IllegalArgumentException("验证等级必须在0-3之间");
        }

        LambdaUpdateWrapper<FishingSpot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FishingSpot::getId, id)
                .set(FishingSpot::getVerificationLevel, level)
                .set(FishingSpot::getUpdatedAt, LocalDateTime.now());

        return baseMapper.update(null, updateWrapper) > 0;
    }


    public boolean incrementVisitorCount(Long id) {
        return baseMapper.incrementVisitorCount(id);
    }


    public boolean incrementCheckinCount(Long id) {
        return baseMapper.incrementCheckinCount(id);
    }


    public boolean updateRating(Long id, BigDecimal rating) {
        if (rating.compareTo(BigDecimal.ZERO) < 0 || rating.compareTo(new BigDecimal("5.0")) > 0) {
            throw new IllegalArgumentException("评分必须在0-5之间");
        }

        LambdaUpdateWrapper<FishingSpot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FishingSpot::getId, id)
                .set(FishingSpot::getRating, rating)
                .set(FishingSpot::getUpdatedAt, LocalDateTime.now());

        return baseMapper.update(null, updateWrapper) > 0;
    }


    public List<FishingSpot> getPopularSpots(int limit) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(FishingSpot::getVisitorCount)
                .last("LIMIT " + limit);

        return baseMapper.selectList(queryWrapper);
    }


    public boolean updateById(FishingSpot entity) {
        return super.updateById(entity);
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveFishTypes(Long spotId, List<Long> fishTypeIds) {
        // 先删除原有关联
        LambdaQueryWrapper<SpotFishTypes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFishTypes::getSpotId, spotId);
        spotFishTypesMapper.delete(queryWrapper);

        // 如果没有新的鱼类ID，直接返回成功
        if (fishTypeIds == null || fishTypeIds.isEmpty()) {
            return;
        }

        // 插入新的关联
        List<SpotFishTypes> associations = new ArrayList<>();
        for (Long fishTypeId : fishTypeIds) {
            SpotFishTypes association = new SpotFishTypes();
            association.setSpotId(spotId);
            association.setFishTypeId(fishTypeId);
            associations.add(association);
        }
        spotFishTypesService.saveBatch(associations);
    }


    public List<Long> getFishTypeIds(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotFishTypes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFishTypes::getSpotId, spotId);
        List<SpotFishTypes> associations = spotFishTypesMapper.selectList(queryWrapper);

        return associations.stream()
                .map(SpotFishTypes::getFishTypeId)
                .collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveSpotImages(Long spotId, List<String> imageUrls, Long uploaderId) {
        if (imageUrls == null || imageUrls.isEmpty()) {
            return;
        }

        // 判断是否需要清除原图片
        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "normal"); // 仅查询普通图片，不包括认证文件
        List<SpotImage> existingImages = spotImageMapper.selectList(queryWrapper);

        // 如果有新图片，并且与现有图片URL不同，则删除旧图片并添加新图片
        if (!existingImages.isEmpty()) {
            List<String> existingUrls = existingImages.stream()
                    .map(SpotImage::getImageUrl)
                    .toList();

            // 如果新旧图片URL完全相同，则不做任何处理
            if (new HashSet<>(existingUrls).containsAll(imageUrls) && new HashSet<>(imageUrls).containsAll(existingUrls)) {
                return;
            }

            // 删除旧图片记录
            spotImageMapper.delete(queryWrapper);
        }

        // 保存新图片记录
        for (int i = 0; i < imageUrls.size(); i++) {
            SpotImage spotImage = new SpotImage();
            spotImage.setSpotId(spotId);
            spotImage.setImageUrl(imageUrls.get(i));
            spotImage.setSortOrder(i); // 使用索引作为排序顺序
            spotImage.setUploadedBy(uploaderId);
            spotImage.setImageType("normal"); // 设置为普通图片类型
            spotImage.setCreatedAt(LocalDateTime.now());
            spotImageMapper.insert(spotImage);
        }
    }


    public List<String> getSpotImages(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "normal") // 只获取普通图片，不包括认证文件
                .orderByAsc(SpotImage::getSortOrder);
        List<SpotImage> images = spotImageMapper.selectList(queryWrapper);

        return images.stream()
                .map(SpotImage::getImageUrl)
                .collect(Collectors.toList());
    }


    /**
     * 获取钓点认证文件
     *
     * @param spotId 钓点ID
     * @return 认证文件URL列表
     */
    public List<String> getSpotCertificationDocuments(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "certification")
                .orderByAsc(SpotImage::getSortOrder);
        List<SpotImage> images = spotImageMapper.selectList(queryWrapper);

        return images.stream()
                .map(SpotImage::getImageUrl)
                .collect(Collectors.toList());
    }


    public IPage<SpotDetailVO> findFishingSpots(QuerySpotDto querySpotDto) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        if (querySpotDto != null) {
            if (StringUtils.hasText(querySpotDto.getFilterType())) {
                switch (querySpotDto.getFilterType()) {
                    case "官方认证":
                        queryWrapper.eq(FishingSpot::getIsOfficial, true);
                        break;
                    case "用户推荐":
                        queryWrapper.eq(FishingSpot::getIsOfficial, false);
                        break;
                    case "免费钓场":
                        queryWrapper.eq(FishingSpot::getIsPaid, false);
                        break;
                    case "付费钓场":
                        queryWrapper.eq(FishingSpot::getIsPaid, true);
                        break;
                    default:
                        break;
                }
            }

            // 按鱼类筛选
            if (querySpotDto.getFishTypes() != null && !querySpotDto.getFishTypes().isEmpty()) {
                queryWrapper.in(FishingSpot::getId, spotFishTypesService.getFishPotIdsByFishTypeNames(querySpotDto.getFishTypes()));
            }

            // 按基础设施筛选
            if (querySpotDto.getHasFacilities() != null) {
                queryWrapper.eq(FishingSpot::getHasFacilities, querySpotDto.getHasFacilities());
            }
            queryWrapper.eq(FishingSpot::getStatus, 1); // 只查询正常状态的钓点
        }

        queryWrapper.orderByDesc(FishingSpot::getCreatedAt);

        Page<FishingSpot> page = new Page<>(querySpotDto.getPageNum(), querySpotDto.getPageSize());
        IPage<FishingSpot> spotPage = baseMapper.selectPage(page, queryWrapper);

        List<SpotDetailVO> spotDetails = convertToSpotDetailVO(spotPage.getRecords());

        IPage<SpotDetailVO> spotDetailVOIPage = new Page<>();
        spotDetailVOIPage.setRecords(spotDetails);
        spotDetailVOIPage.setTotal(spotPage.getTotal());
        spotDetailVOIPage.setCurrent(spotPage.getCurrent());
        spotDetailVOIPage.setSize(spotPage.getSize());
        return spotDetailVOIPage;
    }


    public Optional<SpotDetailVO> findById(Long id) {
        FishingSpot spot = baseMapper.selectById(id);
        if (spot == null) {
            return Optional.empty();
        }

        List<Long> fishTypeIds = getFishTypeIds(id);
        List<FishType> fishTypes = new ArrayList<>();
        if (!fishTypeIds.isEmpty()) {
            fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);
        }

        List<String> imageUrls = getSpotImages(id);
        List<String> certificationDocuments = getSpotCertificationDocuments(id);
        List<SpotPriceVO> prices = getPricesBySpotId(id);
        List<SpotFacilityVO> facilities = getSpotFacilities(id);

        SpotDetailVO spotDetailVO = new SpotDetailVO();
        copyProperties(spot, spotDetailVO);
        spotDetailVO.setFishTypeList(fishTypes);
        spotDetailVO.setImages(imageUrls);
        spotDetailVO.setCertificationDocuments(certificationDocuments);
        spotDetailVO.setPrices(prices);
        spotDetailVO.setFacilities(facilities);

        return Optional.of(spotDetailVO);
    }


    public boolean checkin(Long spotId, Long userId) throws BizException.InvalidOperation {
        boolean checkinToday = spotCheckinService.checkinToday(spotId, userId);
        if (checkinToday) {
            throw new BizException.InvalidOperation("今日已签到");
        }

        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FishingSpot::getId, spotId)
                .eq(FishingSpot::getStatus, 1);
        FishingSpot spot = baseMapper.selectOne(queryWrapper);
        if (spot == null) {
            return false;
        }

        spotCheckinService.checking(spotId, userId);
        incrementCheckinCount(spotId);

        return true;
    }

    public List<SpotPriceVO> getPricesBySpotId(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        // 获取价格数据
        List<SpotPrice> prices = getPricesBySpotId_Raw(spotId);
        if (prices.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有相关的鱼类ID
        List<Long> fishTypeIds = prices.stream()
                .map(SpotPrice::getFishTypeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询鱼类信息
        Map<Long, FishType> fishTypeMap = new HashMap<>();
        if (!fishTypeIds.isEmpty()) {
            List<FishType> fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);
            fishTypeMap = fishTypes.stream().collect(Collectors.toMap(FishType::getId, ft -> ft));
        }

        // 转换为VO
        List<SpotPriceVO> result = new ArrayList<>();
        for (SpotPrice price : prices) {
            SpotPriceVO vo = new SpotPriceVO();
            copyProperties(price, vo);

            // 设置鱼类信息
            if (price.getFishTypeId() != null) {
                vo.setFishType(fishTypeMap.get(price.getFishTypeId()));
            }

            result.add(vo);
        }

        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean savePrices(Long spotId, List<SpotPriceVO> prices) {
        // 先删除原有价格
        LambdaQueryWrapper<SpotPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotPrice::getSpotId, spotId);
        spotPriceMapper.delete(queryWrapper);

        // 如果没有新价格，直接返回
        if (prices == null || prices.isEmpty()) {
            return true;
        }

        // 设置基础信息并保存
        LocalDateTime now = LocalDateTime.now();
        for (SpotPriceVO price : prices) {
            SpotPrice spotPrice = new SpotPrice();
            copyProperties(price, spotPrice);
            spotPrice.setSpotId(spotId);
            spotPrice.setCreatedAt(now);
            spotPrice.setUpdatedAt(now);
            spotPriceMapper.insert(spotPrice);
        }

        return true;
    }


    public List<SpotPrice> getPricesBySpotId_Raw(Long spotId) {
        LambdaQueryWrapper<SpotPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotPrice::getSpotId, spotId);
        return spotPriceMapper.selectList(queryWrapper);
    }


    public List<SpotPrice> getPricesBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpotPrice::getSpotId, spotIds);
        return spotPriceMapper.selectList(queryWrapper);
    }

    /**
     * 获取钓点设施列表
     */
    public List<SpotFacilityVO> getSpotFacilities(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        // 查询钓点设施关联表
        LambdaQueryWrapper<SpotFacility> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFacility::getSpotId, spotId);
        List<SpotFacility> spotFacilities = spotFacilityMapper.selectList(queryWrapper);

        if (spotFacilities.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取设施ID列表
        List<Long> facilityIds = spotFacilities.stream()
                .map(SpotFacility::getFacilityId)
                .collect(Collectors.toList());

        // 查询设施信息
        List<Facility> facilities = facilityMapper.selectBatchIds(facilityIds);

        // 组装设施VO
        Map<Long, Facility> facilityMap = facilities.stream()
                .collect(Collectors.toMap(Facility::getId, f -> f));

        List<SpotFacilityVO> result = new ArrayList<>();
        for (SpotFacility sf : spotFacilities) {
            Facility facility = facilityMap.get(sf.getFacilityId());
            if (facility != null) {
                SpotFacilityVO vo = new SpotFacilityVO();
                vo.setId(facility.getId());
                vo.setName(facility.getName());
                vo.setIcon(facility.getIcon());
                vo.setDescription(facility.getDescription());
                vo.setDetails(sf.getDetails());
                result.add(vo);
            }
        }

        return result;
    }

    /**
     * 获取多个钓点的设施信息
     */
    public Map<Long, List<SpotFacilityVO>> getSpotFacilitiesBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new HashMap<>();
        }

        // 查询所有指定钓点的设施关联
        LambdaQueryWrapper<SpotFacility> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpotFacility::getSpotId, spotIds);
        List<SpotFacility> allSpotFacilities = spotFacilityMapper.selectList(queryWrapper);

        if (allSpotFacilities.isEmpty()) {
            return new HashMap<>();
        }

        // 获取涉及的所有设施ID
        List<Long> facilityIds = allSpotFacilities.stream()
                .map(SpotFacility::getFacilityId)
                .distinct()
                .collect(Collectors.toList());

        // 查询设施信息
        List<Facility> facilities = facilityMapper.selectBatchIds(facilityIds);
        Map<Long, Facility> facilityMap = facilities.stream()
                .collect(Collectors.toMap(Facility::getId, f -> f));

        // 按钓点ID分组
        Map<Long, List<SpotFacility>> spotFacilityMap = allSpotFacilities.stream()
                .collect(Collectors.groupingBy(SpotFacility::getSpotId));

        // 组装结果
        Map<Long, List<SpotFacilityVO>> result = new HashMap<>();
        for (Map.Entry<Long, List<SpotFacility>> entry : spotFacilityMap.entrySet()) {
            Long spotId = entry.getKey();
            List<SpotFacility> spotFacilities = entry.getValue();

            List<SpotFacilityVO> facilityVOList = new ArrayList<>();
            for (SpotFacility sf : spotFacilities) {
                Facility facility = facilityMap.get(sf.getFacilityId());
                if (facility != null) {
                    SpotFacilityVO vo = new SpotFacilityVO();
                    vo.setId(facility.getId());
                    vo.setName(facility.getName());
                    vo.setIcon(facility.getIcon());
                    vo.setDescription(facility.getDescription());
                    vo.setDetails(sf.getDetails());
                    facilityVOList.add(vo);
                }
            }

            result.put(spotId, facilityVOList);
        }

        return result;
    }

    /**
     * 保存钓点设施信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveFacilities(Long spotId, List<SpotFacilityVO> facilities) {
        if (spotId == null) {
            return;
        }

        LambdaQueryWrapper<SpotFacility> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFacility::getSpotId, spotId);
        spotFacilityMapper.delete(queryWrapper);

        if (facilities == null || facilities.isEmpty()) {
            return;
        }

        List<Facility> allFacilitiesFromDb = facilityService.list();
        List<SpotFacility> spotFacilities = new ArrayList<>();
        facilities.forEach(facility -> {
            if (StringUtils.hasText(facility.getName())) {
                Facility facilityFilter = allFacilitiesFromDb.stream().filter(f -> f.getName().equals(facility.getName())).findFirst().orElse(null);
                if (facilityFilter != null) {
                    SpotFacility spotFacility = new SpotFacility();
                    spotFacility.setSpotId(spotId);
                    spotFacility.setFacilityId(facilityFilter.getId());
                    spotFacility.setDetails(facility.getDetails());
                    spotFacilities.add(spotFacility);
                } else {
                    Facility newFacility = new Facility();
                    newFacility.setName(facility.getName());
                    newFacility.setIcon(facility.getIcon());
                    newFacility.setDescription(facility.getDescription());
                    facilityMapper.insert(newFacility);

                    SpotFacility spotFacility = new SpotFacility();
                    spotFacility.setSpotId(spotId);
                    spotFacility.setFacilityId(newFacility.getId());
                    spotFacility.setDetails(facility.getDetails());
                    spotFacilities.add(spotFacility);
                }
            }
        });

        spotFacilityService.saveBatch(spotFacilities);
    }

    /**
     * 将FishingSpot实体列表转换为SpotDetailVO列表
     *
     * @param fishingSpots 钓点实体列表
     * @return 钓点详情VO列表
     */
    public List<SpotDetailVO> convertToSpotDetailVO(List<FishingSpot> fishingSpots) {
        if (fishingSpots == null || fishingSpots.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> spotIds = fishingSpots.stream().map(FishingSpot::getId).toList();
        List<SpotFishTypes> spotFishTypeBySpotIds = spotFishTypesService.getSpotFishTypeBySpotIds(spotIds);
        List<Long> fishTypeIds = spotFishTypeBySpotIds.stream().map(SpotFishTypes::getFishTypeId).toList();
        List<FishType> fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);

        // 查询所有钓点的图片
        List<SpotImage> allSpotImages = spotImageMapper.selectList(
                new LambdaQueryWrapper<SpotImage>()
                        .in(SpotImage::getSpotId, spotIds)
                        .orderByAsc(SpotImage::getSortOrder));

        // 按类型分组图片
        Map<Long, List<String>> normalImagesMap = new HashMap<>();
        Map<Long, List<String>> certificationDocumentsMap = new HashMap<>();

        for (SpotImage image : allSpotImages) {
            if ("normal".equals(image.getImageType())) {
                normalImagesMap.computeIfAbsent(image.getSpotId(), k -> new ArrayList<>())
                        .add(image.getImageUrl());
            } else if ("certification".equals(image.getImageType())) {
                certificationDocumentsMap.computeIfAbsent(image.getSpotId(), k -> new ArrayList<>())
                        .add(image.getImageUrl());
            }
        }

        // 获取所有钓点的价格信息
        List<SpotPrice> allPrices = getPricesBySpotIds(spotIds);
        Map<Long, List<SpotPrice>> spotPriceMap = allPrices.stream()
                .collect(Collectors.groupingBy(SpotPrice::getSpotId));

        // 获取所有相关的鱼类信息
        List<Long> priceFishTypeIds = allPrices.stream()
                .map(SpotPrice::getFishTypeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, FishType> fishTypeMap;
        if (!priceFishTypeIds.isEmpty()) {
            List<FishType> priceFishTypes = fishTypeMapper.selectBatchIds(priceFishTypeIds);
            fishTypeMap = priceFishTypes.stream().collect(Collectors.toMap(FishType::getId, ft -> ft));
        } else {
            fishTypeMap = new HashMap<>();
        }

        // 获取所有钓点的设施信息
        Map<Long, List<SpotFacilityVO>> spotFacilityMap = getSpotFacilitiesBySpotIds(spotIds);

        // 获取动态相关数据
        Map<Long, Integer> recentMomentsCountMap = getRecentMomentsCountBySpotIds(spotIds);
        Map<Long, MomentVO> latestMomentMap = getLatestMomentsBySpotIds(spotIds);

        return fishingSpots.stream()
                .map(spot -> {
                    SpotDetailVO spotDetailVO = new SpotDetailVO();
                    copyProperties(spot, spotDetailVO);

                    // 设置关联的鱼类
                    List<SpotFishTypes> spotFishTypeBySpotId = spotFishTypeBySpotIds.stream().filter(s -> s.getSpotId().equals(spot.getId())).toList();
                    List<Long> fishTypeIdsBySpotId = spotFishTypeBySpotId.stream().map(SpotFishTypes::getFishTypeId).toList();
                    List<FishType> fishTypeList = fishTypes.stream().filter(f -> fishTypeIdsBySpotId.contains(f.getId())).toList();
                    spotDetailVO.setFishTypeList(fishTypeList);

                    // 设置图片
                    spotDetailVO.setImages(normalImagesMap.getOrDefault(spot.getId(), new ArrayList<>()));

                    // 设置认证文件
                    spotDetailVO.setCertificationDocuments(certificationDocumentsMap.getOrDefault(spot.getId(), new ArrayList<>()));

                    // 设置价格信息
                    List<SpotPrice> spotPrices = spotPriceMap.getOrDefault(spot.getId(), new ArrayList<>());
                    if (!spotPrices.isEmpty()) {
                        List<SpotPriceVO> priceVOs = new ArrayList<>();
                        for (SpotPrice price : spotPrices) {
                            SpotPriceVO vo = new SpotPriceVO();
                            copyProperties(price, vo);
                            if (price.getFishTypeId() != null) {
                                vo.setFishType(fishTypeMap.get(price.getFishTypeId()));
                            }
                            priceVOs.add(vo);
                        }
                        spotDetailVO.setPrices(priceVOs);
                    } else {
                        spotDetailVO.setPrices(new ArrayList<>());
                    }

                    // 设置设施信息
                    spotDetailVO.setFacilities(spotFacilityMap.getOrDefault(spot.getId(), new ArrayList<>()));

                    // 设置动态相关信息
                    spotDetailVO.setRecentMomentsCount(recentMomentsCountMap.getOrDefault(spot.getId(), 0));
                    spotDetailVO.setLatestMoment(latestMomentMap.get(spot.getId()));

                    return spotDetailVO;
                })
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePot(Long[] ids) {
        for (Long id : ids) {
            // 删除钓点
            baseMapper.deleteById(id);

            // 删除钓点图片
            LambdaQueryWrapper<SpotImage> imageQueryWrapper = new LambdaQueryWrapper<>();
            imageQueryWrapper.eq(SpotImage::getSpotId, id);
            spotImageMapper.delete(imageQueryWrapper);

            // 删除钓点鱼类关联
            LambdaQueryWrapper<SpotFishTypes> fishTypeQueryWrapper = new LambdaQueryWrapper<>();
            fishTypeQueryWrapper.eq(SpotFishTypes::getSpotId, id);
            spotFishTypesMapper.delete(fishTypeQueryWrapper);

            // 删除钓点价格
            LambdaQueryWrapper<SpotPrice> priceQueryWrapper = new LambdaQueryWrapper<>();
            priceQueryWrapper.eq(SpotPrice::getSpotId, id);
            spotPriceMapper.delete(priceQueryWrapper);

            // 删除钓点设施
            LambdaQueryWrapper<SpotFacility> facilityQueryWrapper = new LambdaQueryWrapper<>();
            facilityQueryWrapper.eq(SpotFacility::getSpotId, id);
            spotFacilityMapper.delete(facilityQueryWrapper);

            LambdaQueryWrapper<SpotCheckin> checkinQueryWrapper = new LambdaQueryWrapper<>();
            checkinQueryWrapper.eq(SpotCheckin::getSpotId, id);
            spotCheckinMapper.delete(checkinQueryWrapper);
        }

    }

    /**
     * 创建钓点
     *
     * @param createDto 钓点创建数据
     * @param userId    创建用户ID
     * @return 创建的钓点ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createSpot(SpotCreateDto createDto, Long userId) {
        FishingSpot spot = createDto.getSpot();
        LocalDateTime now = LocalDateTime.now();
        spot.setCreatedAt(now);
        spot.setUpdatedAt(now);
        spot.setCreatedBy(userId);
        spot.setVisitorCount(0);
        spot.setCheckinCount(0);
        if (spot.getRating() == null) {
            spot.setRating(BigDecimal.ZERO);
        }

        if (createDto.getIsOfficial() != null && createDto.getIsOfficial()) {
            spot.setStatus(2);
        } else {
            spot.setStatus(1);
        }

        if (createDto.getHasFacilities() != null) {
            spot.setHasFacilities(createDto.getHasFacilities());
        }

        if (createDto.getIsPaid() != null) {
            spot.setIsPaid(createDto.getIsPaid());
        }

        if (createDto.getExtraFishTypes() != null && !createDto.getExtraFishTypes().isEmpty()) {
            spot.setExtraFishTypes(String.join(",", createDto.getExtraFishTypes()));
        }

        if (createDto.getExtraFacilities() != null && !createDto.getExtraFacilities().isEmpty()) {
            spot.setExtraFacilities(String.join(",", createDto.getExtraFacilities()));
        }

        baseMapper.insert(spot);
        Long spotId = spot.getId();

        if (createDto.getFishTypeNames() != null && !createDto.getFishTypeNames().isEmpty()) {
            List<Long> fishTypeIds = getExistingFishTypeIdsByNames(createDto.getFishTypeNames());
            if (!fishTypeIds.isEmpty()) {
                saveFishTypes(spotId, fishTypeIds);
            }
        }

        if (createDto.getImageUrls() != null && !createDto.getImageUrls().isEmpty()) {
            saveSpotImages(spotId, createDto.getImageUrls(), userId);
        }

        if (Boolean.TRUE.equals(createDto.getHasFacilities()) &&
                createDto.getFacilities() != null && !createDto.getFacilities().isEmpty()) {
            saveFacilities(spotId, createDto.getFacilities());
        }

        if (Boolean.TRUE.equals(createDto.getIsPaid()) &&
                createDto.getPrices() != null && !createDto.getPrices().isEmpty()) {
            savePrices(spotId, createDto.getPrices());
        }

        if (Boolean.TRUE.equals(createDto.getIsOfficial()) &&
                createDto.getCertificationDocuments() != null && !createDto.getCertificationDocuments().isEmpty()) {
            saveCertificationDocuments(spotId, createDto.getCertificationDocuments(), userId);
        }
        return spotId;
    }

    /**
     * 根据鱼类名称获取已存在的鱼类ID
     *
     * @param fishTypeNames 鱼类名称列表
     * @return 已存在的鱼类ID列表
     */
    private List<Long> getExistingFishTypeIdsByNames(List<String> fishTypeNames) {
        if (fishTypeNames == null || fishTypeNames.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> fishTypeIds = new ArrayList<>();

        for (String fishTypeName : fishTypeNames) {
            LambdaQueryWrapper<FishType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FishType::getName, fishTypeName);
            FishType existingFishType = fishTypeMapper.selectOne(queryWrapper);

            if (existingFishType != null) {
                fishTypeIds.add(existingFishType.getId());
            }
        }

        return fishTypeIds;
    }

    /**
     * 保存认证文件
     *
     * @param spotId                 钓点ID
     * @param certificationDocuments 认证文件URL列表
     * @param userId                 上传用户ID
     */
    public void saveCertificationDocuments(Long spotId, List<String> certificationDocuments, Long userId) {
        if (certificationDocuments == null || certificationDocuments.isEmpty()) {
            return;
        }

        // 先删除原有认证文件
        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "certification");
        spotImageMapper.delete(queryWrapper);

        // 保存新的认证文件
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < certificationDocuments.size(); i++) {
            SpotImage spotImage = new SpotImage();
            spotImage.setSpotId(spotId);
            spotImage.setImageUrl(certificationDocuments.get(i));
            spotImage.setSortOrder(i);
            spotImage.setUploadedBy(userId);
            spotImage.setImageType("certification"); // 设置为认证文件类型
            spotImage.setCreatedAt(now);
            spotImageMapper.insert(spotImage);
        }
    }

    /**
     * 根据鱼类名称保存钓点与鱼类的关联
     *
     * @param spotId        钓点ID
     * @param fishTypeNames 鱼类名称列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveFishTypesByNames(Long spotId, List<String> fishTypeNames) {
        if (fishTypeNames == null || fishTypeNames.isEmpty()) {
            return;
        }

        // 获取已有鱼类的ID
        List<Long> fishTypeIds = getExistingFishTypeIdsByNames(fishTypeNames);

        // 使用已有的saveFishTypes方法保存关联
        saveFishTypes(spotId, fishTypeIds);
    }

    /**
     * 保存自定义鱼类
     *
     * @param spotId         钓点ID
     * @param extraFishTypes 自定义鱼类名称列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveExtraFishTypes(Long spotId, List<String> extraFishTypes) {
        if (spotId == null) {
            return;
        }

        // 获取钓点实体
        FishingSpot spot = baseMapper.selectById(spotId);
        if (spot == null) {
            return;
        }

        if (extraFishTypes == null || extraFishTypes.isEmpty()) {
            // 如果为空，则清除现有的值
            spot.setExtraFishTypes(null);
        } else {
            // 将自定义鱼类名称列表转换为以逗号分隔的字符串
            String extraFishTypesStr = String.join(",", extraFishTypes);
            spot.setExtraFishTypes(extraFishTypesStr);
        }

        // 更新钓点
        baseMapper.updateById(spot);
    }

    /**
     * 保存自定义设施
     *
     * @param spotId          钓点ID
     * @param extraFacilities 自定义设施名称列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveExtraFacilities(Long spotId, List<String> extraFacilities) {
        if (spotId == null) {
            return;
        }

        // 获取钓点实体
        FishingSpot spot = baseMapper.selectById(spotId);
        if (spot == null) {
            return;
        }

        if (extraFacilities == null || extraFacilities.isEmpty()) {
            // 如果为空，则清除现有的值
            spot.setExtraFacilities(null);
        } else {
            // 将自定义设施名称列表转换为以逗号分隔的字符串
            String extraFacilitiesStr = String.join(",", extraFacilities);
            spot.setExtraFacilities(extraFacilitiesStr);
        }

        // 更新钓点
        baseMapper.updateById(spot);
    }

    /**
     * 保存认证文件
     *
     * @param spotId                 钓点ID
     * @param certificationDocuments 认证文件URL列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCertificationDocuments(Long spotId, List<String> certificationDocuments) {
        if (certificationDocuments == null || certificationDocuments.isEmpty()) {
            return;
        }

        // 先删除原有的认证文件
        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "certification");
        spotImageMapper.delete(queryWrapper);

        // 添加新的认证文件
        List<SpotImage> newImages = new ArrayList<>();
        for (String imageUrl : certificationDocuments) {
            if (StringUtils.hasText(imageUrl)) {
                SpotImage spotImage = new SpotImage();
                spotImage.setSpotId(spotId);
                spotImage.setImageUrl(imageUrl);
                spotImage.setImageType("certification"); // 标记为认证文件
                spotImage.setCreatedAt(LocalDateTime.now());
                newImages.add(spotImage);
            }
        }

        // 批量插入新的认证文件
        for (SpotImage spotImage : newImages) {
            spotImageMapper.insert(spotImage);
        }
    }

    /**
     * 获取用户最近签到的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 最近签到的钓点列表
     */
    public List<SpotDetailVO> getUserRecentCheckins(Long userId, int page, int size) {
        // 查询用户的签到记录（按时间倒序）
        LambdaQueryWrapper<SpotCheckin> checkinQueryWrapper = new LambdaQueryWrapper<>();
        checkinQueryWrapper.eq(SpotCheckin::getUserId, userId)
                .orderByDesc(SpotCheckin::getCheckinTime);

        // 分页
        Page<SpotCheckin> checkinPage = new Page<>(page, size);
        Page<SpotCheckin> checkinResult = spotCheckinMapper.selectPage(checkinPage, checkinQueryWrapper);

        if (checkinResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 提取钓点ID
        List<Long> spotIds = checkinResult.getRecords().stream()
                .map(SpotCheckin::getSpotId)
                .collect(Collectors.toList());

        // 查询钓点基本信息
        List<FishingSpot> fishingSpots = baseMapper.selectBatchIds(spotIds);

        // 转换为VO
        return convertToSpotDetailVO(fishingSpots);
    }

    /**
     * 获取用户收藏的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 收藏的钓点列表
     */
    public List<SpotDetailVO> getUserFavorites(Long userId, int page, int size) {
        // 查询用户的收藏记录
        Page<SpotFavorite> favoritePage = new Page<>(page, size);

        LambdaQueryWrapper<SpotFavorite> favoriteQueryWrapper = new LambdaQueryWrapper<>();
        favoriteQueryWrapper.eq(SpotFavorite::getUserId, userId)
                .orderByDesc(SpotFavorite::getCreatedAt);

        Page<SpotFavorite> favoriteResult = spotFavoriteMapper.selectPage(favoritePage, favoriteQueryWrapper);

        if (favoriteResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 提取钓点ID
        List<Long> spotIds = favoriteResult.getRecords().stream()
                .map(SpotFavorite::getSpotId)
                .collect(Collectors.toList());

        // 查询钓点基本信息
        List<FishingSpot> fishingSpots = baseMapper.selectBatchIds(spotIds);

        // 转换为VO
        return convertToSpotDetailVO(fishingSpots);
    }

    /**
     * 获取用户创建的钓点列表
     *
     * @param userId 用户ID
     * @param page   页码 (0-based)
     * @param size   每页数量
     * @return 创建的钓点列表
     */
    public List<SpotDetailVO> getUserCreatedSpots(Long userId, int page, int size) {
        // 查询用户创建的钓点
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FishingSpot::getCreatedBy, userId)
                .eq(FishingSpot::getStatus, 1) // 正常状态的钓点
                .orderByDesc(FishingSpot::getCreatedAt);

        // 分页处理
        Page<FishingSpot> spotPage = new Page<>(page, size);
        Page<FishingSpot> spotResult = baseMapper.selectPage(spotPage, queryWrapper);

        if (spotResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 转换为VO
        return convertToSpotDetailVO(spotResult.getRecords());
    }

    /**
     * 搜索钓点
     *
     * @param query 搜索关键词
     * @param page  页码 (0-based)
     * @param size  每页数量
     * @return 搜索结果
     */
    public List<SpotDetailVO> searchFishingSpots(String query, int page, int size) {
        if (!StringUtils.hasText(query)) {
            return new ArrayList<>();
        }

        // 构建搜索条件
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
                        .like(FishingSpot::getName, query)
                        .or()
                        .like(FishingSpot::getAddress, query)
                        .or()
                        .like(FishingSpot::getDescription, query))
                .eq(FishingSpot::getStatus, 1) // 正常状态的钓点
                .orderByDesc(FishingSpot::getCreatedAt);

        // 分页处理
        Page<FishingSpot> spotPage = new Page<>(page, size);
        Page<FishingSpot> spotResult = baseMapper.selectPage(spotPage, queryWrapper);

        if (spotResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 转换为VO
        return convertToSpotDetailVO(spotResult.getRecords());
    }

    /**
     * 带地理位置的钓点搜索
     *
     * @param query     搜索关键词
     * @param page      页码 (0-based)
     * @param size      每页数量
     * @param latitude  纬度
     * @param longitude 经度
     * @param radiusKm  搜索半径（公里）
     * @return 搜索结果
     */
    public List<SpotDetailVO> searchFishingSpots(
            String query, int page, int size,
            double latitude, double longitude, double radiusKm) {

        // 先获取附近的钓点
        List<FishingSpot> nearbySpots = findNearbySpots(
                new BigDecimal(latitude),
                new BigDecimal(longitude),
                radiusKm);

        // 如果没有提供搜索关键词，直接返回附近的钓点（分页处理）
        if (!StringUtils.hasText(query)) {
            int fromIndex = page * size;
            if (fromIndex >= nearbySpots.size()) {
                return new ArrayList<>();
            }

            int toIndex = Math.min(fromIndex + size, nearbySpots.size());
            List<FishingSpot> pagedSpots = nearbySpots.subList(fromIndex, toIndex);
            return convertToSpotDetailVO(pagedSpots);
        }

        // 对附近的钓点进行关键词过滤
        List<FishingSpot> filteredSpots = nearbySpots.stream()
                .filter(spot ->
                        (spot.getName() != null && spot.getName().contains(query)) ||
                                (spot.getAddress() != null && spot.getAddress().contains(query)) ||
                                (spot.getDescription() != null && spot.getDescription().contains(query)))
                .collect(Collectors.toList());

        // 分页处理
        int fromIndex = page * size;
        if (fromIndex >= filteredSpots.size()) {
            return new ArrayList<>();
        }

        int toIndex = Math.min(fromIndex + size, filteredSpots.size());
        List<FishingSpot> pagedSpots = filteredSpots.subList(fromIndex, toIndex);

        // 转换为VO
        return convertToSpotDetailVO(pagedSpots);
    }

    /**
     * 获取钓点最近动态数量（最近7天）
     *
     * @param spotIds 钓点ID列表
     * @return 钓点ID -> 最近动态数量的映射
     */
    private Map<Long, Integer> getRecentMomentsCountBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 计算7天前的时间
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

            // 查询最近7天的动态
            LambdaQueryWrapper<Moment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Moment::getFishingSpotId, spotIds)
                    .ge(Moment::getCreatedAt, sevenDaysAgo);

            List<Moment> recentMoments = momentMapper.selectList(queryWrapper);

            // 按钓点ID分组并计数
            Map<Long, Integer> countMap = recentMoments.stream()
                    .filter(moment -> moment.getFishingSpotId() != null)
                    .collect(Collectors.groupingBy(
                            Moment::getFishingSpotId,
                            Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));

            return countMap;
        } catch (Exception e) {
            // 记录错误日志，返回空映射
            log.error("获取钓点最近动态数量失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取钓点最新动态
     *
     * @param spotIds 钓点ID列表
     * @return 钓点ID -> 最新动态VO的映射
     */
    private Map<Long, MomentVO> getLatestMomentsBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 查询所有相关动态，按创建时间降序排列
            LambdaQueryWrapper<Moment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Moment::getFishingSpotId, spotIds)
                    .orderByDesc(Moment::getCreatedAt);

            List<Moment> moments = momentMapper.selectList(queryWrapper);

            // 按钓点ID分组，取每组的第一个（最新的）
            Map<Long, Moment> latestMomentsMap = new HashMap<>();
            for (Moment moment : moments) {
                if (moment.getFishingSpotId() != null && 
                    !latestMomentsMap.containsKey(moment.getFishingSpotId())) {
                    latestMomentsMap.put(moment.getFishingSpotId(), moment);
                }
            }

            // 转换为MomentVO
            Map<Long, MomentVO> result = new HashMap<>();
            for (Map.Entry<Long, Moment> entry : latestMomentsMap.entrySet()) {
                MomentVO momentVO = convertToSimpleMomentVO(entry.getValue());
                if (momentVO != null) {
                    result.put(entry.getKey(), momentVO);
                }
            }

            return result;
        } catch (Exception e) {
            // 记录错误日志，返回空映射
            log.error("获取钓点最新动态失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 将Moment对象转换为简化的MomentVO对象（用于钓点卡片展示）
     *
     * @param moment 动态对象
     * @return 简化的动态VO对象
     */
    private MomentVO convertToSimpleMomentVO(Moment moment) {
        if (moment == null) {
            return null;
        }

        try {
            MomentVO vo = new MomentVO();
            BeanUtils.copyProperties(moment, vo);

            // 设置发布者信息
            if (moment.getUserId() != null) {
                User user = userMapper.selectById(moment.getUserId());
                if (user != null) {
                    UserVo publisher = new UserVo();
                    BeanUtils.copyProperties(user, publisher);
                    vo.setPublisher(publisher);
                }
            }

            // 对于钓点卡片，我们只需要基本信息，不需要图片、点赞等详细信息
            // 这样可以提高性能，减少数据传输量
            vo.setImages(new ArrayList<>()); // 空图片列表
            vo.setIsLiked(false); // 默认未点赞

            return vo;
        } catch (Exception e) {
            log.error("转换MomentVO失败，momentId: {}", moment.getId(), e);
            return null;
        }
    }

    // ========== Algolia 同步相关方法实现 ==========

    @Override
    public List<FishingSpot> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(FishingSpot::getUpdatedAt, lastSyncTime)
                .orderByAsc(FishingSpot::getUpdatedAt);

        Page<FishingSpot> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<FishingSpot> result = baseMapper.selectPage(pageParam, queryWrapper);

        return result.getRecords();
    }

    @Override
    public List<FishingSpot> findAll(int page, int size) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(FishingSpot::getId);

        Page<FishingSpot> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<FishingSpot> result = baseMapper.selectPage(pageParam, queryWrapper);

        return result.getRecords();
    }
}

