package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.domain.User;
import com.fishing.mapper.UserMapper;
import com.fishing.service.IUserInfoService;
import com.fishing.vo.UserVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl extends ServiceImpl<UserMapper, User> implements IUserInfoService {

    @Override
    public List<UserVo> getBy(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return List.of();
        }

        List<User> users = listByIds(userIds);
        return parseFrom().andThen(q -> q).apply(users);
    }

    @Override
    public UserVo getBy(Long userId) {
        User user = getById(userId);
        return parseFrom().apply(List.of(user)).stream().findFirst().orElse(null);
    }

    @Override
    public UserVo updateUserInfo(User user) {
        return parseFrom().apply(List.of(user)).stream().findFirst().orElse(null);
    }

    private Function<List<User>, List<UserVo>> parseFrom() {
        return users -> {
            List<Long> userIds = users.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());
            if (userIds.isEmpty()) {
                return List.of();
            }
            return users.stream().map(
                    user -> {
                        UserVo userVo = new UserVo();
                        userVo.setId(user.getId());
                        userVo.setTelephoneNumber(user.getTelephoneNumber());
                        // Skip password and salt for security reasons
                        userVo.setTitle(user.getTitle());
                        userVo.setName(user.getName());
                        userVo.setGender(user.getGender());
                        userVo.setAvatarUrl(user.getAvatarUrl());
                        userVo.setIntroduce(user.getIntroduce());
                        userVo.setProvince(user.getProvince());
                        userVo.setCity(user.getCity());
                        userVo.setCounty(user.getCounty());
                        return userVo;
                    }).collect(Collectors.toList());
        };
    }

    // ========== Algolia 同步相关方法实现 ==========

    @Override
    public Integer getFollowersCount(Long userId) {
        // TODO: 实现获取粉丝数的逻辑，需要关注服务支持
        // 这里返回默认值，实际实现需要调用关注服务
        return 0;
    }

    @Override
    public Integer getFollowingCount(Long userId) {
        // TODO: 实现获取关注数的逻辑，需要关注服务支持
        // 这里返回默认值，实际实现需要调用关注服务
        return 0;
    }

    @Override
    public List<User> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(User::getUpdateTime, lastSyncTime)
                .orderByAsc(User::getUpdateTime);

        Page<User> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<User> result = page(pageParam, wrapper);

        return result.getRecords();
    }

    @Override
    public List<User> findAll(int page, int size) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(User::getId);

        Page<User> pageParam = new Page<>(page + 1, size); // MyBatis-Plus 使用 1 基索引
        Page<User> result = page(pageParam, wrapper);

        return result.getRecords();
    }
}