package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.dto.search.SearchRequestDTO;
import com.fishing.mapper.FishingSpotMapper;
import com.fishing.mapper.MomentMapper;
import com.fishing.mapper.UserMapper;
import com.fishing.service.SearchService;
import com.fishing.vo.search.SearchResultVO;
import com.fishing.vo.search.SearchSuggestionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 搜索服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SearchServiceImpl implements SearchService {

    private static final String SEARCH_HISTORY_KEY = "search:history:";
    private static final String HOT_SEARCH_KEY = "search:hot";
    private static final int MAX_HISTORY_SIZE = 10;
    private final UserMapper userMapper;
    private final MomentMapper momentMapper;
    private final FishingSpotMapper fishingSpotMapper;
    private final StringRedisTemplate redisTemplate;

    @Override
    public Page<SearchResultVO> search(SearchRequestDTO request, Long userId) {
        // 记录搜索行为
        recordSearch(request.getKeyword(), userId);

        Page<SearchResultVO> resultPage = new Page<>(request.getPage(), request.getSize());
        List<SearchResultVO> results = new ArrayList<>();

        String keyword = request.getKeyword().trim();

        // 根据搜索类型进行搜索
        switch (request.getType().toLowerCase()) {
            case "moment":
                results.addAll(searchMoments(keyword, request, userId));
                break;
            case "user":
                results.addAll(searchUsers(keyword, request));
                break;
            case "spot":
                results.addAll(searchFishingSpots(keyword, request));
                break;
            default:
                // 综合搜索
                results.addAll(searchMoments(keyword, request, userId));
                results.addAll(searchUsers(keyword, request));
                results.addAll(searchFishingSpots(keyword, request));
                break;
        }

        // 排序
        results = sortResults(results, request.getSortBy());

        // 分页
        int start = (request.getPage() - 1) * request.getSize();
        int end = Math.min(start + request.getSize(), results.size());

        resultPage.setRecords(results.subList(start, end));
        resultPage.setTotal(results.size());
        resultPage.setSize(request.getSize());
        resultPage.setCurrent(request.getPage());

        return resultPage;
    }

    private List<SearchResultVO> searchMoments(String keyword, SearchRequestDTO request, Long userId) {
        QueryWrapper<Moment> wrapper = new QueryWrapper<>();
        wrapper.like("content", keyword)
                .or()
                .like("title", keyword);

        // 添加位置筛选
        if (StringUtils.hasText(request.getProvince())) {
            wrapper.like("province", request.getProvince());
        }
        if (StringUtils.hasText(request.getCity())) {
            wrapper.like("city", request.getCity());
        }

        // 添加时间筛选
        if (request.getDayRange() != null && request.getDayRange() > 0) {
            LocalDateTime startTime = LocalDateTime.now().minusDays(request.getDayRange());
            wrapper.ge("create_time", startTime);
        }

        wrapper.orderByDesc("create_time");
        wrapper.last("LIMIT 20"); // 限制结果数量

        List<Moment> moments = momentMapper.selectList(wrapper);

        return moments.stream().map(moment -> {
            // 获取作者信息
            User author = userMapper.selectById(moment.getUserId());

            return SearchResultVO.builder()
                    .id(moment.getId())
                    .type("moment")
                    .title(extractTitle(moment.getContent()))
                    .content(moment.getContent())
                    .authorName(author != null ? author.getName() : "未知用户")
                    .authorAvatar(author != null ? author.getAvatarUrl() : null)
                    .location(buildLocation(author != null ? author.getProvince() : null,
                            author != null ? author.getCity() : null,
                            author != null ? author.getCounty() : null))
                    .createTime(moment.getCreatedAt())
                    .likeCount(0) // Will be calculated from related tables
                    .commentCount(0) // Will be calculated from related tables
                    .relevanceScore(calculateRelevance(keyword, moment.getContent()))
                    .highlightContent(highlightKeyword(moment.getContent(), keyword))
                    .build();
        }).collect(Collectors.toList());
    }

    private List<SearchResultVO> searchUsers(String keyword, SearchRequestDTO request) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.like("name", keyword)
                .or()
                .like("introduce", keyword);

        wrapper.orderByDesc("create_time");
        wrapper.last("LIMIT 10");

        List<User> users = userMapper.selectList(wrapper);

        return users.stream().map(user ->
                SearchResultVO.builder()
                        .id(user.getId())
                        .type("user")
                        .title(user.getName())
                        .content(user.getIntroduce() != null ? user.getIntroduce() : "")
                        .authorName(user.getName())
                        .authorAvatar(user.getAvatarUrl())
                        .location(buildLocation(user.getProvince(), user.getCity(), user.getCounty()))
                        .createTime(user.getCreateTime())
                        .relevanceScore(calculateRelevance(keyword, user.getName()))
                        .highlightContent(highlightKeyword(user.getName(), keyword))
                        .build()
        ).collect(Collectors.toList());
    }

    private List<SearchResultVO> searchFishingSpots(String keyword, SearchRequestDTO request) {
        QueryWrapper<FishingSpot> wrapper = new QueryWrapper<>();
        wrapper.like("name", keyword)
                .or()
                .like("description", keyword)
                .or()
                .like("address", keyword);

        if (StringUtils.hasText(request.getProvince())) {
            wrapper.like("province", request.getProvince());
        }
        if (StringUtils.hasText(request.getCity())) {
            wrapper.like("city", request.getCity());
        }

        wrapper.orderByDesc("rating");
        wrapper.last("LIMIT 10");

        List<FishingSpot> spots = fishingSpotMapper.selectList(wrapper);

        return spots.stream().map(spot ->
                SearchResultVO.builder()
                        .id(spot.getId())
                        .type("spot")
                        .title(spot.getName())
                        .content(spot.getDescription())
                        .location(spot.getAddress())
                        .createTime(spot.getCreatedAt())
                        .relevanceScore(calculateRelevance(keyword, spot.getName() + " " + spot.getDescription()))
                        .highlightContent(highlightKeyword(spot.getName(), keyword))
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    public List<SearchSuggestionVO> getSearchSuggestions(String keyword, Integer limit, Long userId) {
        List<SearchSuggestionVO> suggestions = new ArrayList<>();

        // 如果关键词为空或太短，返回热门搜索和默认建议
        if (!StringUtils.hasText(keyword)) {
            return getDefaultSuggestions(limit);
        }

        // 降低最小长度限制到1个字符
        if (keyword.isEmpty()) {
            return suggestions;
        }

        try {
            // 用户名建议
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.like("name", keyword).last("LIMIT 5");
            List<User> users = userMapper.selectList(userWrapper);
            users.forEach(user -> suggestions.add(
                    SearchSuggestionVO.builder()
                            .suggestion(user.getName())
                            .type("user")
                            .extra(user.getAvatarUrl())
                            .build()
            ));

            // 钓点建议
            QueryWrapper<FishingSpot> spotWrapper = new QueryWrapper<>();
            spotWrapper.like("name", keyword).or().like("address", keyword).last("LIMIT 5");
            List<FishingSpot> spots = fishingSpotMapper.selectList(spotWrapper);
            spots.forEach(spot -> suggestions.add(
                    SearchSuggestionVO.builder()
                            .suggestion(spot.getName())
                            .type("spot")
                            .extra(spot.getAddress())
                            .build()
            ));

            // 热门关键词建议
            Set<String> hotKeywords = getHotKeywords();
            hotKeywords.stream()
                    .filter(k -> k.toLowerCase().contains(keyword.toLowerCase()))
                    .limit(3)
                    .forEach(k -> suggestions.add(
                            SearchSuggestionVO.builder()
                                    .suggestion(k)
                                    .type("keyword")
                                    .build()
                    ));

            // 如果没有找到任何建议，返回默认建议
            if (suggestions.isEmpty()) {
                return getDefaultSuggestions(limit);
            }

        } catch (Exception e) {
            log.error("获取搜索建议失败: {}", e.getMessage(), e);
            return getDefaultSuggestions(limit);
        }

        return suggestions.stream().limit(limit).collect(Collectors.toList());
    }

    private List<SearchSuggestionVO> getDefaultSuggestions(Integer limit) {
        List<SearchSuggestionVO> defaultSuggestions = new ArrayList<>();

        try {
            // 获取热门搜索关键词
            Set<String> hotKeywords = getHotKeywords();
            hotKeywords.stream()
                    .limit(Math.min(limit / 2, 5))
                    .forEach(k -> defaultSuggestions.add(
                            SearchSuggestionVO.builder()
                                    .suggestion(k)
                                    .type("keyword")
                                    .build()
                    ));

            // 获取一些热门钓点
            QueryWrapper<FishingSpot> spotWrapper = new QueryWrapper<>();
            spotWrapper.orderByDesc("created_at").last("LIMIT 3");
            List<FishingSpot> spots = fishingSpotMapper.selectList(spotWrapper);
            spots.forEach(spot -> defaultSuggestions.add(
                    SearchSuggestionVO.builder()
                            .suggestion(spot.getName())
                            .type("spot")
                            .extra(spot.getAddress())
                            .build()
            ));

            // 如果还是没有，添加一些默认的搜索建议
            if (defaultSuggestions.isEmpty()) {
                defaultSuggestions.add(SearchSuggestionVO.builder()
                        .suggestion("钓鱼")
                        .type("keyword")
                        .build());
                defaultSuggestions.add(SearchSuggestionVO.builder()
                        .suggestion("钓点")
                        .type("keyword")
                        .build());
                defaultSuggestions.add(SearchSuggestionVO.builder()
                        .suggestion("分享")
                        .type("keyword")
                        .build());
            }

        } catch (Exception e) {
            log.error("获取默认搜索建议失败: {}", e.getMessage(), e);
            // 最后的保底建议
            defaultSuggestions.add(SearchSuggestionVO.builder()
                    .suggestion("钓鱼")
                    .type("keyword")
                    .build());
        }

        return defaultSuggestions.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<String> getSearchHistory(Long userId) {
        String key = SEARCH_HISTORY_KEY + userId;
        List<String> history = redisTemplate.opsForList().range(key, 0, MAX_HISTORY_SIZE - 1);
        return history != null ? history : new ArrayList<>();
    }

    @Override
    public void clearSearchHistory(Long userId) {
        String key = SEARCH_HISTORY_KEY + userId;
        redisTemplate.delete(key);
    }

    @Override
    public List<String> getHotSearchKeywords(Integer limit) {
        Set<String> hotKeywords = getHotKeywords();
        return hotKeywords.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public void recordSearch(String keyword, Long userId) {
        if (!StringUtils.hasText(keyword) || userId == null) {
            return;
        }

        // 记录用户搜索历史
        String historyKey = SEARCH_HISTORY_KEY + userId;
        redisTemplate.opsForList().remove(historyKey, 1, keyword); // 移除重复项
        redisTemplate.opsForList().leftPush(historyKey, keyword);
        redisTemplate.opsForList().trim(historyKey, 0, MAX_HISTORY_SIZE - 1);
        redisTemplate.expire(historyKey, 30, TimeUnit.DAYS);

        // 记录热门搜索
        redisTemplate.opsForZSet().incrementScore(HOT_SEARCH_KEY, keyword, 1);
        redisTemplate.expire(HOT_SEARCH_KEY, 7, TimeUnit.DAYS);
    }

    private Set<String> getHotKeywords() {
        Set<String> hotKeywords = redisTemplate.opsForZSet().reverseRange(HOT_SEARCH_KEY, 0, 19);
        return hotKeywords != null ? hotKeywords : new HashSet<>();
    }

    private List<SearchResultVO> sortResults(List<SearchResultVO> results, String sortBy) {
        return switch (sortBy.toLowerCase()) {
            case "time" -> results.stream()
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .collect(Collectors.toList());
            case "hot" -> results.stream()
                    .sorted((a, b) -> {
                        int aHot = (a.getLikeCount() != null ? a.getLikeCount() : 0) +
                                (a.getCommentCount() != null ? a.getCommentCount() : 0);
                        int bHot = (b.getLikeCount() != null ? b.getLikeCount() : 0) +
                                (b.getCommentCount() != null ? b.getCommentCount() : 0);
                        return Integer.compare(bHot, aHot);
                    })
                    .collect(Collectors.toList());
            default -> // relevance
                    results.stream()
                            .sorted((a, b) -> Double.compare(
                                    b.getRelevanceScore() != null ? b.getRelevanceScore() : 0.0,
                                    a.getRelevanceScore() != null ? a.getRelevanceScore() : 0.0))
                            .collect(Collectors.toList());
        };
    }

    private String extractTitle(String content) {
        if (!StringUtils.hasText(content)) {
            return "";
        }
        return content.length() > 30 ? content.substring(0, 30) + "..." : content;
    }

    private String buildLocation(String province, String city, String county) {
        StringBuilder location = new StringBuilder();
        if (StringUtils.hasText(province)) {
            location.append(province);
        }
        if (StringUtils.hasText(city)) {
            if (!location.isEmpty()) location.append(" ");
            location.append(city);
        }
        if (StringUtils.hasText(county)) {
            if (!location.isEmpty()) location.append(" ");
            location.append(county);
        }
        return location.toString();
    }

    private Double calculateRelevance(String keyword, String content) {
        if (!StringUtils.hasText(content)) {
            return 0.0;
        }

        String lowerKeyword = keyword.toLowerCase();
        String lowerContent = content.toLowerCase();

        // 简单的相关性计算
        double score = 0.0;

        // 完全匹配得分最高
        if (lowerContent.equals(lowerKeyword)) {
            score += 100.0;
        }

        // 开头匹配
        if (lowerContent.startsWith(lowerKeyword)) {
            score += 50.0;
        }

        // 包含匹配
        int count = 0;
        int index = 0;
        while ((index = lowerContent.indexOf(lowerKeyword, index)) != -1) {
            count++;
            index += lowerKeyword.length();
        }
        score += count * 10.0;

        // 长度影响（内容越短，相关性越高）
        score += Math.max(0, 100 - content.length()) * 0.1;

        return score;
    }

    private String highlightKeyword(String content, String keyword) {
        if (!StringUtils.hasText(content) || !StringUtils.hasText(keyword)) {
            return content;
        }

        // 简单的高亮实现
        return content.replaceAll("(?i)" + keyword, "<mark>" + keyword + "</mark>");
    }
}
