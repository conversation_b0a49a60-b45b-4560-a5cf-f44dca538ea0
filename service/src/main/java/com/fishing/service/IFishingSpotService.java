package com.fishing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.spot.FishingSpot;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 钓点服务接口
 */
public interface IFishingSpotService extends IService<FishingSpot> {
    
    // ========== Algolia 同步相关方法 ==========
    
    /**
     * 查找指定时间之后更新的钓点
     *
     * @param lastSyncTime 最后同步时间
     * @param page         页码
     * @param size         每页大小
     * @return 钓点列表
     */
    List<FishingSpot> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size);
    
    /**
     * 分页查找所有钓点
     *
     * @param page 页码
     * @param size 每页大小
     * @return 钓点列表
     */
    List<FishingSpot> findAll(int page, int size);
}
