package com.fishing.service;

import com.algolia.api.SearchClient;
import com.algolia.model.search.SearchIndex;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.dto.algolia.FishingSpotIndexData;
import com.fishing.dto.algolia.MomentIndexData;
import com.fishing.dto.algolia.UserIndexData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Algolia 索引服务
 * 负责将数据同步到 Algolia 搜索引擎
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AlgoliaIndexService {
    
    private final SearchClient algoliaClient;
    private final MomentService momentService;
    private final UserService userService;
    private final FishingSpotService fishingSpotService;
    
    // 索引名称常量
    private static final String MOMENTS_INDEX = "moments";
    private static final String USERS_INDEX = "users";
    private static final String FISHING_SPOTS_INDEX = "fishing_spots";
    
    /**
     * 同步单个动态到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncMoment(Moment moment) {
        try {
            MomentIndexData indexData = convertMomentToIndexData(moment);
            if (indexData != null) {
                SearchIndex index = algoliaClient.initIndex(MOMENTS_INDEX);
                index.saveObject(indexData);
                log.info("同步动态到 Algolia 成功: {}", moment.getId());
            }
        } catch (Exception e) {
            log.error("同步动态到 Algolia 失败: {}", moment.getId(), e);
            throw e;
        }
    }
    
    /**
     * 批量同步动态到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void batchSyncMoments(List<Moment> moments) {
        try {
            List<MomentIndexData> indexDataList = moments.stream()
                .map(this::convertMomentToIndexData)
                .filter(data -> data != null)
                .collect(Collectors.toList());
                
            if (!indexDataList.isEmpty()) {
                SearchIndex index = algoliaClient.initIndex(MOMENTS_INDEX);
                index.saveObjects(indexDataList);
                log.info("批量同步 {} 个动态到 Algolia 成功", indexDataList.size());
            }
        } catch (Exception e) {
            log.error("批量同步动态到 Algolia 失败", e);
            throw e;
        }
    }
    
    /**
     * 删除动态索引
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void deleteMoment(Long momentId) {
        try {
            SearchIndex index = algoliaClient.initIndex(MOMENTS_INDEX);
            index.deleteObject("moment_" + momentId);
            log.info("从 Algolia 删除动态成功: {}", momentId);
        } catch (Exception e) {
            log.error("从 Algolia 删除动态失败: {}", momentId, e);
            throw e;
        }
    }
    
    /**
     * 同步单个用户到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncUser(User user) {
        try {
            UserIndexData indexData = convertUserToIndexData(user);
            if (indexData != null) {
                SearchIndex index = algoliaClient.initIndex(USERS_INDEX);
                index.saveObject(indexData);
                log.info("同步用户到 Algolia 成功: {}", user.getId());
            }
        } catch (Exception e) {
            log.error("同步用户到 Algolia 失败: {}", user.getId(), e);
            throw e;
        }
    }
    
    /**
     * 批量同步用户到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void batchSyncUsers(List<User> users) {
        try {
            List<UserIndexData> indexDataList = users.stream()
                .map(this::convertUserToIndexData)
                .filter(data -> data != null)
                .collect(Collectors.toList());
                
            if (!indexDataList.isEmpty()) {
                SearchIndex index = algoliaClient.initIndex(USERS_INDEX);
                index.saveObjects(indexDataList);
                log.info("批量同步 {} 个用户到 Algolia 成功", indexDataList.size());
            }
        } catch (Exception e) {
            log.error("批量同步用户到 Algolia 失败", e);
            throw e;
        }
    }
    
    /**
     * 删除用户索引
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void deleteUser(Long userId) {
        try {
            SearchIndex index = algoliaClient.initIndex(USERS_INDEX);
            index.deleteObject("user_" + userId);
            log.info("从 Algolia 删除用户成功: {}", userId);
        } catch (Exception e) {
            log.error("从 Algolia 删除用户失败: {}", userId, e);
            throw e;
        }
    }
    
    /**
     * 同步单个钓点到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void syncFishingSpot(FishingSpot spot) {
        try {
            FishingSpotIndexData indexData = convertFishingSpotToIndexData(spot);
            if (indexData != null) {
                SearchIndex index = algoliaClient.initIndex(FISHING_SPOTS_INDEX);
                index.saveObject(indexData);
                log.info("同步钓点到 Algolia 成功: {}", spot.getId());
            }
        } catch (Exception e) {
            log.error("同步钓点到 Algolia 失败: {}", spot.getId(), e);
            throw e;
        }
    }
    
    /**
     * 批量同步钓点到 Algolia
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void batchSyncFishingSpots(List<FishingSpot> spots) {
        try {
            List<FishingSpotIndexData> indexDataList = spots.stream()
                .map(this::convertFishingSpotToIndexData)
                .filter(data -> data != null)
                .collect(Collectors.toList());
                
            if (!indexDataList.isEmpty()) {
                SearchIndex index = algoliaClient.initIndex(FISHING_SPOTS_INDEX);
                index.saveObjects(indexDataList);
                log.info("批量同步 {} 个钓点到 Algolia 成功", indexDataList.size());
            }
        } catch (Exception e) {
            log.error("批量同步钓点到 Algolia 失败", e);
            throw e;
        }
    }
    
    /**
     * 删除钓点索引
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void deleteFishingSpot(Long spotId) {
        try {
            SearchIndex index = algoliaClient.initIndex(FISHING_SPOTS_INDEX);
            index.deleteObject("spot_" + spotId);
            log.info("从 Algolia 删除钓点成功: {}", spotId);
        } catch (Exception e) {
            log.error("从 Algolia 删除钓点失败: {}", spotId, e);
            throw e;
        }
    }
    
    /**
     * 清空所有索引
     */
    public void clearAllIndexes() {
        try {
            algoliaClient.initIndex(MOMENTS_INDEX).clearObjects();
            algoliaClient.initIndex(USERS_INDEX).clearObjects();
            algoliaClient.initIndex(FISHING_SPOTS_INDEX).clearObjects();
            log.info("清空所有 Algolia 索引成功");
        } catch (Exception e) {
            log.error("清空 Algolia 索引失败", e);
            throw e;
        }
    }
    
    /**
     * 转换动态为索引数据
     */
    private MomentIndexData convertMomentToIndexData(Moment moment) {
        if (moment == null) {
            return null;
        }
        
        try {
            // 获取作者信息
            User author = userService.getById(moment.getUserId());
            if (author == null) {
                log.warn("动态 {} 的作者 {} 不存在", moment.getId(), moment.getUserId());
                return null;
            }
            
            // 获取钓点信息
            String fishingSpotName = null;
            if (moment.getFishingSpotId() != null) {
                FishingSpot spot = fishingSpotService.getById(moment.getFishingSpotId());
                if (spot != null) {
                    fishingSpotName = spot.getName();
                }
            }
            
            // 获取统计信息
            Integer likeCount = momentService.getLikeCount(moment.getId());
            Integer commentCount = momentService.getCommentCount(moment.getId());
            
            return MomentIndexData.builder()
                .objectID("moment_" + moment.getId())
                .id(moment.getId())
                .title(MomentIndexData.extractTitle(moment.getContent()))
                .content(moment.getContent())
                .authorId(author.getId())
                .authorName(author.getName())
                .authorAvatar(author.getAvatarUrl())
                .momentType(moment.getMomentType())
                .location(UserIndexData.buildLocation(author.getProvince(), author.getCity(), author.getCounty()))
                .province(author.getProvince())
                .city(author.getCity())
                .county(author.getCounty())
                .fishingSpotId(moment.getFishingSpotId())
                .fishingSpotName(fishingSpotName)
                .likeCount(likeCount != null ? likeCount : 0)
                .commentCount(commentCount != null ? commentCount : 0)
                .visibility(moment.getVisibility())
                .createdAt(moment.getCreatedAt() != null ? moment.getCreatedAt().toEpochSecond(ZoneOffset.UTC) : null)
                .updatedAt(moment.getUpdatedAt() != null ? moment.getUpdatedAt().toEpochSecond(ZoneOffset.UTC) : null)
                .isActive(true)
                .hotScore(MomentIndexData.calculateHotScore(likeCount, commentCount, moment.getCreatedAt()))
                .build();
                
        } catch (Exception e) {
            log.error("转换动态 {} 为索引数据失败", moment.getId(), e);
            return null;
        }
    }
    
    /**
     * 转换用户为索引数据
     */
    private UserIndexData convertUserToIndexData(User user) {
        if (user == null) {
            return null;
        }
        
        try {
            // 获取用户统计信息
            Integer followersCount = userService.getFollowersCount(user.getId());
            Integer followingCount = userService.getFollowingCount(user.getId());
            Integer momentsCount = momentService.getMomentsCountByUserId(user.getId());
            
            return UserIndexData.builder()
                .objectID("user_" + user.getId())
                .id(user.getId())
                .name(user.getName())
                .bio(user.getIntroduce())
                .avatarUrl(user.getAvatarUrl())
                .level(user.getTitle())
                .location(UserIndexData.buildLocation(user.getProvince(), user.getCity(), user.getCounty()))
                .province(user.getProvince())
                .city(user.getCity())
                .county(user.getCounty())
                .followersCount(followersCount != null ? followersCount : 0)
                .followingCount(followingCount != null ? followingCount : 0)
                .momentsCount(momentsCount != null ? momentsCount : 0)
                .createdAt(user.getCreateTime() != null ? user.getCreateTime().toEpochSecond(ZoneOffset.UTC) : null)
                .isActive(true)
                .activityScore(UserIndexData.calculateActivityScore(followersCount, momentsCount, user.getCreateTime()))
                .gender(user.getGender())
                .maskedPhone(UserIndexData.maskPhoneNumber(user.getTelephoneNumber()))
                .build();
                
        } catch (Exception e) {
            log.error("转换用户 {} 为索引数据失败", user.getId(), e);
            return null;
        }
    }
    
    /**
     * 转换钓点为索引数据
     */
    private FishingSpotIndexData convertFishingSpotToIndexData(FishingSpot spot) {
        if (spot == null) {
            return null;
        }
        
        try {
            return FishingSpotIndexData.builder()
                .objectID("spot_" + spot.getId())
                .id(spot.getId())
                .name(spot.getName())
                .description(spot.getDescription())
                .address(spot.getAddress())
                .province(spot.getProvince())
                .city(spot.getCity())
                .county(spot.getCounty())
                .fishTypes(FishingSpotIndexData.parseFishTypes(null, spot.getExtraFishTypes()))
                .facilities(FishingSpotIndexData.parseFacilities(null, spot.getExtraFacilities()))
                .rating(spot.getRating())
                .reviewCount(0) // TODO: 从评价服务获取
                .priceRange(FishingSpotIndexData.determinePriceRange(spot.getIsPaid()))
                .createdAt(spot.getCreatedAt() != null ? spot.getCreatedAt().toEpochSecond(ZoneOffset.UTC) : null)
                .isActive(spot.getStatus() != null && spot.getStatus() == 1)
                .isOfficial(spot.getIsOfficial())
                .verificationLevel(spot.getVerificationLevel())
                .visitorCount(spot.getVisitorCount())
                .checkinCount(spot.getCheckinCount())
                .geoloc(FishingSpotIndexData.createGeoloc(spot.getLatitude(), spot.getLongitude()))
                .isPaid(spot.getIsPaid())
                .hasFacilities(spot.getHasFacilities())
                .popularityScore(FishingSpotIndexData.calculatePopularityScore(
                    spot.getRating(), 0, spot.getVisitorCount(), spot.getCheckinCount(),
                    spot.getIsOfficial(), spot.getVerificationLevel()))
                .build();
                
        } catch (Exception e) {
            log.error("转换钓点 {} 为索引数据失败", spot.getId(), e);
            return null;
        }
    }
}
