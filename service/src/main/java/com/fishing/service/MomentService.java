package com.fishing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.moment.Moment;
import com.fishing.vo.moment.MomentVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态服务接口
 */
public interface MomentService extends IService<Moment> {

    /**
     * 创建动态
     *
     * @param moment    动态信息
     * @param imageUrls 图片URL列表
     * @return 创建的动态ID
     */
    Long createMoment(Moment moment, List<String> imageUrls);

    /**
     * 更新动态
     *
     * @param moment    动态信息
     * @param imageUrls 图片URL列表
     * @return 是否更新成功
     */
    boolean updateMoment(Moment moment, List<String> imageUrls);

    /**
     * 删除动态
     *
     * @param momentId 动态ID
     * @param userId   操作用户ID（用于权限验证）
     * @return 是否删除成功
     */
    boolean deleteMoment(Long momentId, Long userId);

    /**
     * 获取动态详情
     *
     * @param momentId      动态ID
     * @param currentUserId 当前用户ID
     * @return 动态详情
     */
    MomentVO getMomentDetail(Long momentId, Long currentUserId);

    /**
     * 分页获取用户动态列表
     *
     * @param userId        用户ID
     * @param page          分页参数
     * @param currentUserId 当前用户ID
     * @return 分页动态列表
     */
    Page<MomentVO> getUserMoments(Long userId, Page<Moment> page, Long currentUserId);

    /**
     * 分页获取关注用户的动态列表
     *
     * @param userId 当前用户ID
     * @param page   分页参数
     * @return 分页动态列表
     */
    Page<MomentVO> getFollowingMoments(Long userId, Page<Moment> page);

    /**
     * 分页获取推荐动态列表
     *
     * @param page          分页参数
     * @param currentUserId 当前用户ID
     * @return 分页动态列表
     */
    Page<MomentVO> getRecommendedMoments(Page<Moment> page, Long currentUserId);

    /**
     * 按类型分页获取动态列表
     *
     * @param momentType    动态类型
     * @param page          分页参数
     * @param currentUserId 当前用户ID
     * @return 分页动态列表
     */
    Page<MomentVO> getMomentsByType(String momentType, Page<Moment> page, Long currentUserId);

    /**
     * 按钓点分页获取动态列表
     *
     * @param fishingSpotId 钓点ID
     * @param page          分页参数
     * @param currentUserId 当前用户ID
     * @return 分页动态列表
     */
    Page<MomentVO> getMomentsByFishingSpot(Long fishingSpotId, Page<Moment> page, Long currentUserId);

    /**
     * 根据条件分页获取动态列表
     *
     * @param filterUserId  筛选用户ID（可选）
     * @param tag          标签筛选（可选）
     * @param province     省份筛选（可选）
     * @param city         城市筛选（可选）
     * @param county       县区筛选（可选）
     * @param momentType   动态类型筛选（可选）
     * @param page         分页参数
     * @param currentUserId 当前用户ID（可选，用于获取用户特定数据）
     * @return 分页动态列表
     */
    Page<MomentVO> getMomentsWithFilters(Long filterUserId, String tag, String province, String city, String county, String momentType, Page<Moment> page, Long currentUserId);

    // ========== Algolia 同步相关方法 ==========

    /**
     * 获取动态点赞数
     *
     * @param momentId 动态ID
     * @return 点赞数
     */
    Integer getLikeCount(Long momentId);

    /**
     * 获取动态评论数
     *
     * @param momentId 动态ID
     * @return 评论数
     */
    Integer getCommentCount(Long momentId);

    /**
     * 获取用户的动态数量
     *
     * @param userId 用户ID
     * @return 动态数量
     */
    Integer getMomentsCountByUserId(Long userId);

    /**
     * 获取用户的所有动态
     *
     * @param userId 用户ID
     * @return 动态列表
     */
    List<Moment> getMomentsByUserId(Long userId);

    /**
     * 获取用户的所有动态ID
     *
     * @param userId 用户ID
     * @return 动态ID列表
     */
    List<Long> getMomentIdsByUserId(Long userId);

    /**
     * 获取钓点相关的所有动态
     *
     * @param spotId 钓点ID
     * @return 动态列表
     */
    List<Moment> getMomentsBySpotId(Long spotId);

    /**
     * 查找指定时间之后更新的动态
     *
     * @param lastSyncTime 最后同步时间
     * @param page         页码
     * @param size         每页大小
     * @return 动态列表
     */
    List<Moment> findUpdatedSince(LocalDateTime lastSyncTime, int page, int size);

    /**
     * 分页查找所有动态
     *
     * @param page 页码
     * @param size 每页大小
     * @return 动态列表
     */
    List<Moment> findAll(int page, int size);
}