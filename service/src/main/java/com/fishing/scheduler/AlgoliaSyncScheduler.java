package com.fishing.scheduler;

import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.service.AlgoliaIndexService;
import com.fishing.service.FishingSpotService;
import com.fishing.service.MomentService;
import com.fishing.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Algolia 同步定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "algolia.sync.enabled", havingValue = "true", matchIfMissing = true)
public class AlgoliaSyncScheduler {
    
    private final AlgoliaIndexService algoliaIndexService;
    private final MomentService momentService;
    private final UserService userService;
    private final FishingSpotService fishingSpotService;
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final String LAST_SYNC_TIME_KEY = "algolia:last_sync_time";
    private static final String SYNC_LOCK_KEY = "algolia:sync_lock";
    private static final int BATCH_SIZE = 1000;
    private static final int LOCK_TIMEOUT = 3600; // 1小时
    
    /**
     * 增量同步 - 每15分钟执行一次
     * 同步最近更新的数据
     */
    @Scheduled(fixedRate = 15 * 60 * 1000) // 15分钟
    public void incrementalSync() {
        String lockKey = SYNC_LOCK_KEY + ":incremental";
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "locked", LOCK_TIMEOUT, TimeUnit.SECONDS);
                
            if (!Boolean.TRUE.equals(lockAcquired)) {
                log.debug("增量同步任务已在其他实例运行，跳过");
                return;
            }
            
            log.info("开始 Algolia 增量同步");
            LocalDateTime lastSyncTime = getLastSyncTime();
            LocalDateTime currentTime = LocalDateTime.now();
            
            // 同步更新的动态
            syncUpdatedMoments(lastSyncTime);
            
            // 同步更新的用户
            syncUpdatedUsers(lastSyncTime);
            
            // 同步更新的钓点
            syncUpdatedFishingSpots(lastSyncTime);
            
            // 更新最后同步时间
            updateLastSyncTime(currentTime);
            
            log.info("Algolia 增量同步完成");
            
        } catch (Exception e) {
            log.error("Algolia 增量同步失败", e);
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 全量同步 - 每天凌晨2点执行
     * 重建所有索引数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void fullSync() {
        String lockKey = SYNC_LOCK_KEY + ":full";
        
        try {
            // 获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "locked", LOCK_TIMEOUT * 2, TimeUnit.SECONDS);
                
            if (!Boolean.TRUE.equals(lockAcquired)) {
                log.warn("全量同步任务已在其他实例运行，跳过");
                return;
            }
            
            log.info("开始 Algolia 全量同步");
            
            // 清空现有索引
            algoliaIndexService.clearAllIndexes();
            
            // 全量同步动态
            fullSyncMoments();
            
            // 全量同步用户
            fullSyncUsers();
            
            // 全量同步钓点
            fullSyncFishingSpots();
            
            // 更新最后同步时间
            updateLastSyncTime(LocalDateTime.now());
            
            log.info("Algolia 全量同步完成");
            
        } catch (Exception e) {
            log.error("Algolia 全量同步失败", e);
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 健康检查 - 每小时执行一次
     * 检查同步状态和数据一致性
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void healthCheck() {
        try {
            log.debug("开始 Algolia 同步健康检查");
            
            // 检查最后同步时间
            LocalDateTime lastSyncTime = getLastSyncTime();
            if (lastSyncTime != null) {
                long minutesSinceLastSync = java.time.Duration.between(lastSyncTime, LocalDateTime.now()).toMinutes();
                
                if (minutesSinceLastSync > 30) {
                    log.warn("Algolia 同步延迟：距离上次同步已过去 {} 分钟", minutesSinceLastSync);
                }
            }
            
            // TODO: 添加更多健康检查逻辑
            // - 检查索引数据量
            // - 检查同步错误率
            // - 检查 API 响应时间
            
            log.debug("Algolia 同步健康检查完成");
            
        } catch (Exception e) {
            log.error("Algolia 同步健康检查失败", e);
        }
    }
    
    /**
     * 同步更新的动态
     */
    private void syncUpdatedMoments(LocalDateTime lastSyncTime) {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<Moment> moments = momentService.findUpdatedSince(lastSyncTime, page, BATCH_SIZE);
                if (moments.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncMoments(moments);
                totalSynced += moments.size();
                page++;
                
                // 避免过度占用资源
                Thread.sleep(1000);
            }
            
            if (totalSynced > 0) {
                log.info("增量同步了 {} 个动态", totalSynced);
            }
            
        } catch (Exception e) {
            log.error("同步更新的动态失败", e);
        }
    }
    
    /**
     * 同步更新的用户
     */
    private void syncUpdatedUsers(LocalDateTime lastSyncTime) {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<User> users = userService.findUpdatedSince(lastSyncTime, page, BATCH_SIZE);
                if (users.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncUsers(users);
                totalSynced += users.size();
                page++;
                
                Thread.sleep(1000);
            }
            
            if (totalSynced > 0) {
                log.info("增量同步了 {} 个用户", totalSynced);
            }
            
        } catch (Exception e) {
            log.error("同步更新的用户失败", e);
        }
    }
    
    /**
     * 同步更新的钓点
     */
    private void syncUpdatedFishingSpots(LocalDateTime lastSyncTime) {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<FishingSpot> spots = fishingSpotService.findUpdatedSince(lastSyncTime, page, BATCH_SIZE);
                if (spots.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncFishingSpots(spots);
                totalSynced += spots.size();
                page++;
                
                Thread.sleep(1000);
            }
            
            if (totalSynced > 0) {
                log.info("增量同步了 {} 个钓点", totalSynced);
            }
            
        } catch (Exception e) {
            log.error("同步更新的钓点失败", e);
        }
    }
    
    /**
     * 全量同步动态
     */
    private void fullSyncMoments() {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<Moment> moments = momentService.findAll(page, BATCH_SIZE);
                if (moments.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncMoments(moments);
                totalSynced += moments.size();
                page++;
                
                Thread.sleep(2000); // 全量同步时间间隔更长
            }
            
            log.info("全量同步了 {} 个动态", totalSynced);
            
        } catch (Exception e) {
            log.error("全量同步动态失败", e);
        }
    }
    
    /**
     * 全量同步用户
     */
    private void fullSyncUsers() {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<User> users = userService.findAll(page, BATCH_SIZE);
                if (users.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncUsers(users);
                totalSynced += users.size();
                page++;
                
                Thread.sleep(2000);
            }
            
            log.info("全量同步了 {} 个用户", totalSynced);
            
        } catch (Exception e) {
            log.error("全量同步用户失败", e);
        }
    }
    
    /**
     * 全量同步钓点
     */
    private void fullSyncFishingSpots() {
        try {
            int page = 0;
            int totalSynced = 0;
            
            while (true) {
                List<FishingSpot> spots = fishingSpotService.findAll(page, BATCH_SIZE);
                if (spots.isEmpty()) {
                    break;
                }
                
                algoliaIndexService.batchSyncFishingSpots(spots);
                totalSynced += spots.size();
                page++;
                
                Thread.sleep(2000);
            }
            
            log.info("全量同步了 {} 个钓点", totalSynced);
            
        } catch (Exception e) {
            log.error("全量同步钓点失败", e);
        }
    }
    
    /**
     * 获取最后同步时间
     */
    private LocalDateTime getLastSyncTime() {
        try {
            String timeStr = redisTemplate.opsForValue().get(LAST_SYNC_TIME_KEY);
            if (timeStr != null) {
                return LocalDateTime.parse(timeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
        } catch (Exception e) {
            log.warn("获取最后同步时间失败", e);
        }
        
        // 如果没有记录，返回24小时前
        return LocalDateTime.now().minusHours(24);
    }
    
    /**
     * 更新最后同步时间
     */
    private void updateLastSyncTime(LocalDateTime time) {
        try {
            String timeStr = time.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            redisTemplate.opsForValue().set(LAST_SYNC_TIME_KEY, timeStr);
        } catch (Exception e) {
            log.error("更新最后同步时间失败", e);
        }
    }
}
