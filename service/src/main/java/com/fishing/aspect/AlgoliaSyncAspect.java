package com.fishing.aspect;

import com.fishing.event.AlgoliaEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Algolia 同步切面
 * 自动监听数据变更操作，触发同步事件
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class AlgoliaSyncAspect {
    
    private final ApplicationEventPublisher eventPublisher;
    
    /**
     * 监听动态服务的保存操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.MomentService.save(..))", returning = "result")
    public void afterMomentSave(JoinPoint joinPoint, Object result) {
        try {
            if (result instanceof com.fishing.domain.moment.Moment) {
                com.fishing.domain.moment.Moment moment = (com.fishing.domain.moment.Moment) result;
                eventPublisher.publishEvent(new AlgoliaEvent.MomentCreatedEvent(this, moment.getId()));
                log.debug("发布动态创建事件: {}", moment.getId());
            }
        } catch (Exception e) {
            log.error("发布动态创建事件失败", e);
        }
    }
    
    /**
     * 监听动态服务的更新操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.MomentService.updateById(..))", returning = "result")
    public void afterMomentUpdate(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof com.fishing.domain.moment.Moment) {
                    com.fishing.domain.moment.Moment moment = (com.fishing.domain.moment.Moment) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.MomentUpdatedEvent(this, moment.getId()));
                    log.debug("发布动态更新事件: {}", moment.getId());
                }
            }
        } catch (Exception e) {
            log.error("发布动态更新事件失败", e);
        }
    }
    
    /**
     * 监听动态服务的删除操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.MomentService.removeById(..))", returning = "result")
    public void afterMomentDelete(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof Long) {
                    Long momentId = (Long) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.MomentDeletedEvent(this, momentId));
                    log.debug("发布动态删除事件: {}", momentId);
                }
            }
        } catch (Exception e) {
            log.error("发布动态删除事件失败", e);
        }
    }
    
    /**
     * 监听用户服务的保存操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.UserService.save(..))", returning = "result")
    public void afterUserSave(JoinPoint joinPoint, Object result) {
        try {
            if (result instanceof com.fishing.domain.User) {
                com.fishing.domain.User user = (com.fishing.domain.User) result;
                eventPublisher.publishEvent(new AlgoliaEvent.UserCreatedEvent(this, user.getId()));
                log.debug("发布用户创建事件: {}", user.getId());
            }
        } catch (Exception e) {
            log.error("发布用户创建事件失败", e);
        }
    }
    
    /**
     * 监听用户服务的更新操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.UserService.updateById(..))", returning = "result")
    public void afterUserUpdate(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof com.fishing.domain.User) {
                    com.fishing.domain.User user = (com.fishing.domain.User) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.UserUpdatedEvent(this, user.getId()));
                    log.debug("发布用户更新事件: {}", user.getId());
                }
            }
        } catch (Exception e) {
            log.error("发布用户更新事件失败", e);
        }
    }
    
    /**
     * 监听用户服务的删除操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.UserService.removeById(..))", returning = "result")
    public void afterUserDelete(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof Long) {
                    Long userId = (Long) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.UserDeletedEvent(this, userId));
                    log.debug("发布用户删除事件: {}", userId);
                }
            }
        } catch (Exception e) {
            log.error("发布用户删除事件失败", e);
        }
    }
    
    /**
     * 监听钓点服务的保存操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.FishingSpotService.save(..))", returning = "result")
    public void afterFishingSpotSave(JoinPoint joinPoint, Object result) {
        try {
            if (result instanceof com.fishing.domain.spot.FishingSpot) {
                com.fishing.domain.spot.FishingSpot spot = (com.fishing.domain.spot.FishingSpot) result;
                eventPublisher.publishEvent(new AlgoliaEvent.FishingSpotCreatedEvent(this, spot.getId()));
                log.debug("发布钓点创建事件: {}", spot.getId());
            }
        } catch (Exception e) {
            log.error("发布钓点创建事件失败", e);
        }
    }
    
    /**
     * 监听钓点服务的更新操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.FishingSpotService.updateById(..))", returning = "result")
    public void afterFishingSpotUpdate(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof com.fishing.domain.spot.FishingSpot) {
                    com.fishing.domain.spot.FishingSpot spot = (com.fishing.domain.spot.FishingSpot) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.FishingSpotUpdatedEvent(this, spot.getId()));
                    log.debug("发布钓点更新事件: {}", spot.getId());
                }
            }
        } catch (Exception e) {
            log.error("发布钓点更新事件失败", e);
        }
    }
    
    /**
     * 监听钓点服务的删除操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.FishingSpotService.removeById(..))", returning = "result")
    public void afterFishingSpotDelete(JoinPoint joinPoint, Object result) {
        try {
            if (Boolean.TRUE.equals(result)) {
                Object[] args = joinPoint.getArgs();
                if (args.length > 0 && args[0] instanceof Long) {
                    Long spotId = (Long) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.FishingSpotDeletedEvent(this, spotId));
                    log.debug("发布钓点删除事件: {}", spotId);
                }
            }
        } catch (Exception e) {
            log.error("发布钓点删除事件失败", e);
        }
    }
    
    /**
     * 监听点赞服务操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.MomentLikeService.*(..))")
    public void afterLikeOperation(JoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            
            // 根据方法名和参数判断是否需要更新动态统计
            if (("like".equals(methodName) || "unlike".equals(methodName)) && args.length > 0) {
                if (args[0] instanceof Long) {
                    Long momentId = (Long) args[0];
                    eventPublisher.publishEvent(new AlgoliaEvent.MomentStatsUpdatedEvent(this, momentId));
                    log.debug("发布动态统计更新事件: {}", momentId);
                }
            }
        } catch (Exception e) {
            log.error("发布点赞统计更新事件失败", e);
        }
    }
    
    /**
     * 监听评论服务操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.MomentCommentService.*(..))")
    public void afterCommentOperation(JoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            
            // 根据方法名和参数判断是否需要更新动态统计
            if (("save".equals(methodName) || "removeById".equals(methodName)) && args.length > 0) {
                // 这里需要根据实际的评论实体结构来获取 momentId
                // 假设评论实体有 getMomentId() 方法
                log.debug("评论操作触发，方法: {}", methodName);
                // TODO: 实现具体的 momentId 提取逻辑
            }
        } catch (Exception e) {
            log.error("发布评论统计更新事件失败", e);
        }
    }
    
    /**
     * 监听关注服务操作
     */
    @AfterReturning(pointcut = "execution(* com.fishing.service.FollowService.*(..))")
    public void afterFollowOperation(JoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            Object[] args = joinPoint.getArgs();
            
            // 根据方法名和参数判断是否需要更新用户统计
            if (("follow".equals(methodName) || "unfollow".equals(methodName)) && args.length >= 2) {
                if (args[0] instanceof Long && args[1] instanceof Long) {
                    Long followerId = (Long) args[0];
                    Long followeeId = (Long) args[1];
                    
                    // 更新关注者和被关注者的统计
                    eventPublisher.publishEvent(new AlgoliaEvent.UserStatsUpdatedEvent(this, followerId));
                    eventPublisher.publishEvent(new AlgoliaEvent.UserStatsUpdatedEvent(this, followeeId));
                    
                    log.debug("发布用户关注统计更新事件: {} -> {}", followerId, followeeId);
                }
            }
        } catch (Exception e) {
            log.error("发布关注统计更新事件失败", e);
        }
    }
}
