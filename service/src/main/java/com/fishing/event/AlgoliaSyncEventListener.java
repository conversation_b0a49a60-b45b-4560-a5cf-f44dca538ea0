package com.fishing.event;

import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.service.AlgoliaIndexService;
import com.fishing.service.impl.FishingSpotService;
import com.fishing.service.MomentService;
import com.fishing.service.IUserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Algolia 同步事件监听器
 * 监听数据变更事件，自动同步到 Algolia
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AlgoliaSyncEventListener {
    
    private final AlgoliaIndexService algoliaIndexService;
    private final MomentService momentService;
    private final IUserInfoService userInfoService;
    private final FishingSpotService fishingSpotService;
    
    /**
     * 处理动态创建事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleMomentCreated(AlgoliaEvent.MomentCreatedEvent event) {
        try {
            log.info("处理动态创建事件: {}", event.getEntityId());
            Moment moment = momentService.getById(event.getEntityId());
            if (moment != null) {
                algoliaIndexService.syncMoment(moment);
            } else {
                log.warn("动态 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理动态创建事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理动态更新事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleMomentUpdated(AlgoliaEvent.MomentUpdatedEvent event) {
        try {
            log.info("处理动态更新事件: {}", event.getEntityId());
            Moment moment = momentService.getById(event.getEntityId());
            if (moment != null) {
                algoliaIndexService.syncMoment(moment);
            } else {
                log.warn("动态 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理动态更新事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理动态删除事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleMomentDeleted(AlgoliaEvent.MomentDeletedEvent event) {
        try {
            log.info("处理动态删除事件: {}", event.getEntityId());
            algoliaIndexService.deleteMoment(event.getEntityId());
        } catch (Exception e) {
            log.error("处理动态删除事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理动态统计更新事件（点赞、评论数变化）
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleMomentStatsUpdated(AlgoliaEvent.MomentStatsUpdatedEvent event) {
        try {
            log.debug("处理动态统计更新事件: {}", event.getEntityId());
            Moment moment = momentService.getById(event.getEntityId());
            if (moment != null) {
                algoliaIndexService.syncMoment(moment);
            }
        } catch (Exception e) {
            log.error("处理动态统计更新事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理用户创建事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleUserCreated(AlgoliaEvent.UserCreatedEvent event) {
        try {
            log.info("处理用户创建事件: {}", event.getEntityId());
            User user = userInfoService.getById(event.getEntityId());
            if (user != null) {
                algoliaIndexService.syncUser(user);
            } else {
                log.warn("用户 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理用户创建事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理用户更新事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleUserUpdated(AlgoliaEvent.UserUpdatedEvent event) {
        try {
            log.info("处理用户更新事件: {}", event.getEntityId());
            User user = userInfoService.getById(event.getEntityId());
            if (user != null) {
                algoliaIndexService.syncUser(user);
                
                // 用户信息更新时，也需要更新相关的动态索引
                updateRelatedMoments(event.getEntityId());
            } else {
                log.warn("用户 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理用户更新事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理用户删除事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleUserDeleted(AlgoliaEvent.UserDeletedEvent event) {
        try {
            log.info("处理用户删除事件: {}", event.getEntityId());
            algoliaIndexService.deleteUser(event.getEntityId());
            
            // 用户删除时，删除相关的动态索引
            deleteRelatedMoments(event.getEntityId());
        } catch (Exception e) {
            log.error("处理用户删除事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理用户统计更新事件（粉丝数、关注数变化）
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleUserStatsUpdated(AlgoliaEvent.UserStatsUpdatedEvent event) {
        try {
            log.debug("处理用户统计更新事件: {}", event.getEntityId());
            User user = userInfoService.getById(event.getEntityId());
            if (user != null) {
                algoliaIndexService.syncUser(user);
            }
        } catch (Exception e) {
            log.error("处理用户统计更新事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理钓点创建事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleFishingSpotCreated(AlgoliaEvent.FishingSpotCreatedEvent event) {
        try {
            log.info("处理钓点创建事件: {}", event.getEntityId());
            FishingSpot spot = fishingSpotService.getById(event.getEntityId());
            if (spot != null) {
                algoliaIndexService.syncFishingSpot(spot);
            } else {
                log.warn("钓点 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理钓点创建事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理钓点更新事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleFishingSpotUpdated(AlgoliaEvent.FishingSpotUpdatedEvent event) {
        try {
            log.info("处理钓点更新事件: {}", event.getEntityId());
            FishingSpot spot = fishingSpotService.getById(event.getEntityId());
            if (spot != null) {
                algoliaIndexService.syncFishingSpot(spot);
                
                // 钓点信息更新时，也需要更新相关的动态索引
                updateRelatedMomentsBySpot(event.getEntityId());
            } else {
                log.warn("钓点 {} 不存在，跳过同步", event.getEntityId());
            }
        } catch (Exception e) {
            log.error("处理钓点更新事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 处理钓点删除事件
     */
    @EventListener
    @Async("algoliaTaskExecutor")
    public void handleFishingSpotDeleted(AlgoliaEvent.FishingSpotDeletedEvent event) {
        try {
            log.info("处理钓点删除事件: {}", event.getEntityId());
            algoliaIndexService.deleteFishingSpot(event.getEntityId());
            
            // 钓点删除时，更新相关动态的钓点信息
            updateRelatedMomentsBySpot(event.getEntityId());
        } catch (Exception e) {
            log.error("处理钓点删除事件失败: {}", event.getEntityId(), e);
        }
    }
    
    /**
     * 更新用户相关的动态索引
     */
    private void updateRelatedMoments(Long userId) {
        try {
            // 获取用户的所有动态并重新同步
            var moments = momentService.getMomentsByUserId(userId);
            if (!moments.isEmpty()) {
                algoliaIndexService.batchSyncMoments(moments);
                log.info("更新用户 {} 相关的 {} 个动态索引", userId, moments.size());
            }
        } catch (Exception e) {
            log.error("更新用户 {} 相关动态索引失败", userId, e);
        }
    }
    
    /**
     * 删除用户相关的动态索引
     */
    private void deleteRelatedMoments(Long userId) {
        try {
            // 获取用户的所有动态ID并删除索引
            var momentIds = momentService.getMomentIdsByUserId(userId);
            for (Long momentId : momentIds) {
                algoliaIndexService.deleteMoment(momentId);
            }
            log.info("删除用户 {} 相关的 {} 个动态索引", userId, momentIds.size());
        } catch (Exception e) {
            log.error("删除用户 {} 相关动态索引失败", userId, e);
        }
    }
    
    /**
     * 更新钓点相关的动态索引
     */
    private void updateRelatedMomentsBySpot(Long spotId) {
        try {
            // 获取钓点相关的所有动态并重新同步
            var moments = momentService.getMomentsBySpotId(spotId);
            if (!moments.isEmpty()) {
                algoliaIndexService.batchSyncMoments(moments);
                log.info("更新钓点 {} 相关的 {} 个动态索引", spotId, moments.size());
            }
        } catch (Exception e) {
            log.error("更新钓点 {} 相关动态索引失败", spotId, e);
        }
    }
}
