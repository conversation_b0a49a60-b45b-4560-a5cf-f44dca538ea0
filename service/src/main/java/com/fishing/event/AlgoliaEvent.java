package com.fishing.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Algolia 同步事件基类
 */
@Getter
public abstract class AlgoliaEvent extends ApplicationEvent {
    
    private final String eventType;
    private final Long entityId;
    
    public AlgoliaEvent(Object source, String eventType, Long entityId) {
        super(source);
        this.eventType = eventType;
        this.entityId = entityId;
    }
    
    /**
     * 动态相关事件
     */
    public static class MomentCreatedEvent extends AlgoliaEvent {
        public MomentCreatedEvent(Object source, Long momentId) {
            super(source, "MOMENT_CREATED", momentId);
        }
    }
    
    public static class MomentUpdatedEvent extends AlgoliaEvent {
        public MomentUpdatedEvent(Object source, Long momentId) {
            super(source, "MOMENT_UPDATED", momentId);
        }
    }
    
    public static class MomentDeletedEvent extends AlgoliaEvent {
        public MomentDeletedEvent(Object source, Long momentId) {
            super(source, "MOMENT_DELETED", momentId);
        }
    }
    
    /**
     * 用户相关事件
     */
    public static class UserCreatedEvent extends AlgoliaEvent {
        public UserCreatedEvent(Object source, Long userId) {
            super(source, "USER_CREATED", userId);
        }
    }
    
    public static class UserUpdatedEvent extends AlgoliaEvent {
        public UserUpdatedEvent(Object source, Long userId) {
            super(source, "USER_UPDATED", userId);
        }
    }
    
    public static class UserDeletedEvent extends AlgoliaEvent {
        public UserDeletedEvent(Object source, Long userId) {
            super(source, "USER_DELETED", userId);
        }
    }
    
    /**
     * 钓点相关事件
     */
    public static class FishingSpotCreatedEvent extends AlgoliaEvent {
        public FishingSpotCreatedEvent(Object source, Long spotId) {
            super(source, "FISHING_SPOT_CREATED", spotId);
        }
    }
    
    public static class FishingSpotUpdatedEvent extends AlgoliaEvent {
        public FishingSpotUpdatedEvent(Object source, Long spotId) {
            super(source, "FISHING_SPOT_UPDATED", spotId);
        }
    }
    
    public static class FishingSpotDeletedEvent extends AlgoliaEvent {
        public FishingSpotDeletedEvent(Object source, Long spotId) {
            super(source, "FISHING_SPOT_DELETED", spotId);
        }
    }
    
    /**
     * 统计数据更新事件（点赞、评论等）
     */
    public static class MomentStatsUpdatedEvent extends AlgoliaEvent {
        public MomentStatsUpdatedEvent(Object source, Long momentId) {
            super(source, "MOMENT_STATS_UPDATED", momentId);
        }
    }
    
    public static class UserStatsUpdatedEvent extends AlgoliaEvent {
        public UserStatsUpdatedEvent(Object source, Long userId) {
            super(source, "USER_STATS_UPDATED", userId);
        }
    }
}
