package com.fishing.vo.search;

import lombok.Data;
import lombok.Builder;

/**
 * 搜索建议VO
 */
@Data
@Builder
public class SearchSuggestionVO {
    
    /**
     * 建议内容
     */
    private String suggestion;
    
    /**
     * 建议类型：keyword, user, spot
     */
    private String type;
    
    /**
     * 匹配数量
     */
    private Integer count;
    
    /**
     * 额外信息（如用户头像、钓点位置等）
     */
    private String extra;
}
