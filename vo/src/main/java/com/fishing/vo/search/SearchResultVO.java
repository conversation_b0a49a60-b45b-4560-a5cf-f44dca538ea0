package com.fishing.vo.search;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;

/**
 * 搜索结果VO
 */
@Data
@Builder
public class SearchResultVO {
    
    /**
     * 结果ID
     */
    private Long id;
    
    /**
     * 结果类型：moment, user, spot
     */
    private String type;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 作者信息
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 封面图片
     */
    private String coverImage;
    
    /**
     * 位置信息
     */
    private String location;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论数
     */
    private Integer commentCount;
    
    /**
     * 相关性分数
     */
    private Double relevanceScore;
    
    /**
     * 高亮字段
     */
    private String highlightContent;
}
