import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:user_app/services/algolia_search_service.dart';
import 'package:user_app/models/search/search_models.dart';

// 生成 Mock 类
@GenerateMocks([SearchClient])
import 'algolia_search_service_test.mocks.dart';

void main() {
  group('AlgoliaSearchService', () {
    late AlgoliaSearchService searchService;
    late MockSearchClient mockSearchClient;

    setUp(() {
      mockSearchClient = MockSearchClient();
      // 注意：这里需要修改 AlgoliaSearchService 以支持依赖注入
      // searchService = AlgoliaSearchService(mockSearchClient);
    });

    group('search', () {
      test('should return empty results for empty keyword', () async {
        // Arrange
        const keyword = '';

        // Act
        final result = await searchService.search(keyword);

        // Assert
        expect(result.records, isEmpty);
        expect(result.total, equals(0));
      });

      test('should return search results for valid keyword', () async {
        // Arrange
        const keyword = '鲫鱼';
        final mockResponse = SearchResponse(
          hits: [
            {
              'objectID': 'moment_1',
              'id': 1,
              'content': '今天钓到了大鲫鱼',
              'authorName': '钓鱼达人',
              'createdAt': 1703123456,
            }
          ],
          nbHits: 1,
          page: 0,
          nbPages: 1,
        );

        when(mockSearchClient.searchIndex(request: anyNamed('request')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final result = await searchService.search(keyword);

        // Assert
        expect(result.records, isNotEmpty);
        expect(result.records.first.content, contains('鲫鱼'));
      });

      test('should handle search errors gracefully', () async {
        // Arrange
        const keyword = '测试';
        when(mockSearchClient.searchIndex(request: anyNamed('request')))
            .thenThrow(Exception('Network error'));

        // Act
        final result = await searchService.search(keyword);

        // Assert
        expect(result.records, isEmpty);
        expect(result.total, equals(0));
      });
    });

    group('getSearchSuggestions', () {
      test('should return default suggestions for empty keyword', () async {
        // Act
        final suggestions = await searchService.getSearchSuggestions('');

        // Assert
        expect(suggestions, isNotEmpty);
        expect(suggestions.first.type, equals('keyword'));
      });

      test('should return search suggestions for valid keyword', () async {
        // Arrange
        const keyword = '鲫';
        final mockResponse = SearchResponse(
          hits: [
            {
              'name': '鲫鱼钓法',
              'content': '鲫鱼钓鱼技巧',
            }
          ],
          nbHits: 1,
        );

        when(mockSearchClient.searchIndex(request: anyNamed('request')))
            .thenAnswer((_) async => mockResponse);

        // Act
        final suggestions = await searchService.getSearchSuggestions(keyword);

        // Assert
        expect(suggestions, isNotEmpty);
        expect(suggestions.first.suggestion, contains('鲫'));
      });
    });

    group('search history', () {
      test('should add keyword to search history', () async {
        // Act
        final result = await searchService.addToSearchHistory('鲫鱼');

        // Assert
        expect(result, isTrue);
        final history = await searchService.getSearchHistory();
        expect(history, contains('鲫鱼'));
      });

      test('should not add empty keyword to history', () async {
        // Act
        final result = await searchService.addToSearchHistory('');

        // Assert
        expect(result, isFalse);
      });

      test('should clear search history', () async {
        // Arrange
        await searchService.addToSearchHistory('鲫鱼');

        // Act
        final result = await searchService.clearSearchHistory();

        // Assert
        expect(result, isTrue);
        final history = await searchService.getSearchHistory();
        expect(history, isEmpty);
      });
    });

    group('hot keywords', () {
      test('should return default hot keywords', () async {
        // Act
        final keywords = await searchService.getHotKeywords();

        // Assert
        expect(keywords, isNotEmpty);
        expect(keywords, contains('鲫鱼'));
        expect(keywords, contains('野钓'));
      });
    });

    tearDown(() {
      searchService.dispose();
    });
  });
}

// 辅助类用于测试
class TestSearchResponse extends SearchResponse {
  TestSearchResponse({
    required List<Map<String, dynamic>> hits,
    required int nbHits,
    int page = 0,
    int nbPages = 1,
  }) : super(
          hits: hits,
          nbHits: nbHits,
          page: page,
          nbPages: nbPages,
          hitsPerPage: hits.length,
          processingTimeMS: 1,
          query: '',
          params: '',
        );
}
