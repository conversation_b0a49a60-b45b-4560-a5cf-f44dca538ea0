class SearchRequest {
  final String keyword;
  final String type;
  final String sortBy;
  final int page;
  final int size;
  final String? province;
  final String? city;
  final int? dayRange;

  SearchRequest({
    required this.keyword,
    this.type = 'all',
    this.sortBy = 'relevance',
    this.page = 1,
    this.size = 10,
    this.province,
    this.city,
    this.dayRange,
  });

  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'type': type,
      'sortBy': sortBy,
      'page': page,
      'size': size,
      if (province != null) 'province': province,
      if (city != null) 'city': city,
      if (dayRange != null) 'dayRange': dayRange,
    };
  }
}

class SearchResultVO {
  final int id;
  final String type;
  final String title;
  final String content;
  final String? authorName;
  final String? authorAvatar;
  final String? coverImage;
  final String? location;
  final DateTime? createTime;
  final int? likeCount;
  final int? commentCount;
  final double? relevanceScore;
  final String? highlightContent;

  SearchResultVO({
    required this.id,
    required this.type,
    required this.title,
    required this.content,
    this.authorName,
    this.authorAvatar,
    this.coverImage,
    this.location,
    this.createTime,
    this.likeCount,
    this.commentCount,
    this.relevanceScore,
    this.highlightContent,
  });

  factory SearchResultVO.fromJson(Map<String, dynamic> json) {
    return SearchResultVO(
      id: json['id'],
      type: json['type'],
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      authorName: json['authorName'],
      authorAvatar: json['authorAvatar'],
      coverImage: json['coverImage'],
      location: json['location'],
      createTime: json['createTime'] != null
          ? DateTime.parse(json['createTime'])
          : null,
      likeCount: json['likeCount'],
      commentCount: json['commentCount'],
      relevanceScore: json['relevanceScore']?.toDouble(),
      highlightContent: json['highlightContent'],
    );
  }
}

class SearchSuggestionVO {
  final String suggestion;
  final String type;
  final int? count;
  final String? extra;

  SearchSuggestionVO({
    required this.suggestion,
    required this.type,
    this.count,
    this.extra,
  });

  factory SearchSuggestionVO.fromJson(Map<String, dynamic> json) {
    return SearchSuggestionVO(
      suggestion: json['suggestion'],
      type: json['type'],
      count: json['count'],
      extra: json['extra'],
    );
  }
}

class SearchPage<T> {
  final List<T> records;
  final int total;
  final int size;
  final int current;

  SearchPage({
    required this.records,
    required this.total,
    required this.size,
    required this.current,
  });

  factory SearchPage.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return SearchPage<T>(
      records: (json['records'] as List)
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList(),
      total: json['total'],
      size: json['size'],
      current: json['current'],
    );
  }
}
