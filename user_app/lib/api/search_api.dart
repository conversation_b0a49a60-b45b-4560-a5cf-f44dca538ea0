import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/core/network/api_response.dart';
import 'package:user_app/models/search/search_models.dart';

class SearchApi extends BaseApi {
  SearchApi(super.dio);

  /// 综合搜索
  Future<ApiResponse<SearchPage<SearchResultVO>>> search(
    SearchRequest request,
  ) async {
    return safeApiCall(
      () => dio.post('/search', data: request.toJson()),
      (data) => SearchPage.fromJson(
        data as Map<String, dynamic>,
        (json) => SearchResultVO.fromJson(json),
      ),
    );
  }

  /// 获取搜索建议
  Future<ApiResponse<List<SearchSuggestionVO>>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    return safeApiCall(
      () => dio.get('/search/suggestions', queryParameters: {
        'keyword': keyword,
        'limit': limit,
      }),
      (data) => (data as List)
          .map((item) =>
              SearchSuggestionVO.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 获取搜索历史
  Future<ApiResponse<List<String>>> getSearchHistory() async {
    return safeApiCall(
      () => dio.get('/search/history'),
      (data) => (data as List).cast<String>(),
    );
  }

  /// 清除搜索历史
  Future<ApiResponse<void>> clearSearchHistory() async {
    return safeApiCall(
      () => dio.delete('/search/history'),
    );
  }

  /// 获取热门搜索
  Future<ApiResponse<List<String>>> getHotSearchKeywords({
    int limit = 10,
  }) async {
    return safeApiCall(
      () => dio.get('/search/hot', queryParameters: {
        'limit': limit,
      }),
      (data) => (data as List).cast<String>(),
    );
  }

  /// 记录搜索行为
  Future<ApiResponse<void>> recordSearch(String keyword) async {
    return safeApiCall(
      () => dio.post('/search/record', queryParameters: {
        'keyword': keyword,
      }),
    );
  }
}
