import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:user_app/api/search_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/search/search_models.dart';
import 'package:user_app/services/search_service.dart';

class CommunitySearchPage extends StatefulWidget {
  const CommunitySearchPage({super.key});

  @override
  State<CommunitySearchPage> createState() => _CommunitySearchPageState();
}

class _CommunitySearchPageState extends State<CommunitySearchPage>
    with TickerProviderStateMixin {
  late SearchService _searchService;
  late TextEditingController _searchController;
  late AnimationController _filterAnimationController;
  late AnimationController _searchAnimationController;

  List<SearchResultVO> _searchResults = [];
  List<SearchSuggestionVO> _suggestions = [];
  List<String> _searchHistory = [];
  List<String> _hotKeywords = [];

  bool _isLoading = false;
  bool _showSuggestions = false;
  bool _isSearching = false;
  String _selectedType = 'all';
  String _selectedSort = 'relevance';
  int _currentPage = 1;
  int _totalCount = 0;

  final List<Map<String, dynamic>> _searchTypes = [
    {'key': 'all', 'name': '全部', 'icon': Icons.apps_rounded},
    {'key': 'moment', 'name': '动态', 'icon': Icons.article_rounded},
    {'key': 'user', 'name': '用户', 'icon': Icons.person_rounded},
    {'key': 'spot', 'name': '钓点', 'icon': Icons.location_on_rounded},
  ];

  final List<Map<String, String>> _sortTypes = [
    {'key': 'relevance', 'name': '相关性'},
    {'key': 'time', 'name': '时间'},
    {'key': 'hot', 'name': '热度'},
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _initSearchService();
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterAnimationController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _initSearchService() {
    final dio = getIt<Dio>();
    final searchApi = SearchApi(dio);
    _searchService = SearchService(searchApi);
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadSearchHistory(),
      _loadHotKeywords(),
    ]);
  }

  Future<void> _loadSearchHistory() async {
    try {
      final history = await _searchService.getSearchHistory();
      setState(() {
        _searchHistory = history;
      });
    } catch (e) {
      debugPrint('加载搜索历史失败: $e');
    }
  }

  Future<void> _loadHotKeywords() async {
    try {
      final keywords = await _searchService.getHotKeywords();
      setState(() {
        _hotKeywords = keywords;
      });
    } catch (e) {
      debugPrint('加载热门搜索失败: $e');
    }
  }

  Future<void> _performSearch(String keyword, {bool reset = true}) async {
    if (keyword.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
      _showSuggestions = false;
      if (reset) {
        _searchResults = [];
        _currentPage = 1;
      }
      _isLoading = true;
    });

    _searchAnimationController.forward();

    try {
      final result = await _searchService.search(
        keyword: keyword,
        type: _selectedType,
        sortBy: _selectedSort,
        page: _currentPage,
        pageSize: 20,
      );

      await _searchService.addToSearchHistory(keyword);

      setState(() {
        if (reset) {
          _searchResults = result.records;
        } else {
          _searchResults.addAll(result.records);
        }
        _totalCount = result.total;
        _currentPage++;
        _isLoading = false;
      });

      if (_searchResults.isNotEmpty) {
        _filterAnimationController.forward();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        if (!reset) _currentPage--;
      });
      _showErrorSnackBar('搜索失败，请重试');
    }
  }

  Future<void> _loadMoreResults() async {
    if (_isLoading || _searchResults.length >= _totalCount) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _searchService.search(
        keyword: _searchController.text,
        type: _selectedType,
        sortBy: _selectedSort,
        page: _currentPage,
        pageSize: 20,
      );

      setState(() {
        _searchResults.addAll(result.records);
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _currentPage--;
      });
    }
  }

  Future<void> _updateSuggestions(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _suggestions = [];
        _showSuggestions = false;
        _isSearching = false;
      });
      _searchAnimationController.reverse();
      _filterAnimationController.reverse();
      return;
    }

    if (keyword.length < 1) return;

    try {
      final suggestions = await _searchService.getSearchSuggestions(keyword);
      setState(() {
        _suggestions = suggestions;
        _showSuggestions = suggestions.isNotEmpty;
      });
    } catch (e) {
      debugPrint('获取搜索建议失败: $e');
    }
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _performSearch(suggestion);
  }

  void _selectHistoryKeyword(String keyword) {
    _searchController.text = keyword;
    _performSearch(keyword);
  }

  Future<void> _clearSearchHistory() async {
    final success = await _searchService.clearSearchHistory();
    if (success) {
      setState(() {
        _searchHistory = [];
      });
      _showSuccessSnackBar('搜索历史已清除');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red.shade400,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade400,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.grey.shade800,
        title: _buildSearchBar(),
        surfaceTintColor: Colors.transparent,
      ),
      body: RefreshIndicator(
        onRefresh: () => _performSearch(_searchController.text),
        child: Column(
          children: [
            // 优化的筛选栏
            AnimatedBuilder(
              animation: _filterAnimationController,
              builder: (context, child) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, -1),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: _filterAnimationController,
                    curve: Curves.easeOutCubic,
                  )),
                  child: FadeTransition(
                    opacity: _filterAnimationController,
                    child: _isSearching && _searchResults.isNotEmpty
                        ? _buildModernFilterBar()
                        : const SizedBox.shrink(),
                  ),
                );
              },
            ),

            // 主要内容区域
            Expanded(
              child: _showSuggestions
                  ? _buildModernSuggestionsView()
                  : _isSearching
                      ? _searchResults.isEmpty && _isLoading
                          ? _buildLoadingState()
                          : _searchResults.isEmpty
                              ? _buildNoResultsState()
                              : _buildModernSearchResults()
                      : _buildModernEmptyState(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 44,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(22),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _updateSuggestions,
        onSubmitted: _performSearch,
        style: const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          hintText: '搜索动态、用户、钓点...',
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 16,
          ),
          prefixIcon: AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _searchAnimationController.value * 0.5,
                child: Icon(
                  Icons.search_rounded,
                  color: Colors.grey.shade600,
                  size: 22,
                ),
              );
            },
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _updateSuggestions('');
                  },
                  icon: Icon(
                    Icons.clear_rounded,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildModernFilterBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '找到 $_totalCount 个结果',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              _buildSortButton(),
            ],
          ),
          const SizedBox(height: 12),
          _buildTypeFilters(),
        ],
      ),
    );
  }

  Widget _buildTypeFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _searchTypes.map((type) {
          final isSelected = type['key'] == _selectedType;
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedType = type['key']!;
                  });
                  _performSearch(_searchController.text);
                },
                borderRadius: BorderRadius.circular(20),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? Colors.blue.shade500 : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? Colors.blue.shade500
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        type['icon'],
                        size: 16,
                        color: isSelected ? Colors.white : Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        type['name']!,
                        style: TextStyle(
                          color:
                              isSelected ? Colors.white : Colors.grey.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      initialValue: _selectedSort,
      onSelected: (value) {
        setState(() {
          _selectedSort = value;
        });
        _performSearch(_searchController.text);
      },
      itemBuilder: (context) => _sortTypes.map((sort) {
        return PopupMenuItem(
          value: sort['key'],
          child: Row(
            children: [
              Icon(
                _selectedSort == sort['key']
                    ? Icons.radio_button_checked_rounded
                    : Icons.radio_button_unchecked_rounded,
                size: 16,
                color: _selectedSort == sort['key']
                    ? Colors.blue.shade500
                    : Colors.grey.shade400,
              ),
              const SizedBox(width: 8),
              Text(sort['name']!),
            ],
          ),
        );
      }).toList(),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      offset: const Offset(0, 8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort_rounded,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              _sortTypes.firstWhere((e) => e['key'] == _selectedSort)['name']!,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 2),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernSuggestionsView() {
    return Container(
      color: Colors.white,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _suggestions.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: Colors.grey.shade100,
          indent: 56,
        ),
        itemBuilder: (context, index) {
          final suggestion = _suggestions[index];
          return ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            leading: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: _getSuggestionColor(suggestion.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Icon(
                _getSuggestionIcon(suggestion.type),
                size: 18,
                color: _getSuggestionColor(suggestion.type),
              ),
            ),
            title: Text(
              suggestion.suggestion,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: suggestion.extra != null
                ? Text(
                    suggestion.extra!,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey.shade600,
                    ),
                  )
                : null,
            trailing: Icon(
              Icons.north_west_rounded,
              size: 16,
              color: Colors.grey.shade400,
            ),
            onTap: () => _selectSuggestion(suggestion.suggestion),
          );
        },
      ),
    );
  }

  Widget _buildModernEmptyState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            _buildSectionHeader(
              '搜索历史',
              Icons.history_rounded,
              onClear: _clearSearchHistory,
            ),
            const SizedBox(height: 12),
            _buildChipGrid(_searchHistory, onTap: _selectHistoryKeyword),
            const SizedBox(height: 32),
          ],

          // 热门搜索
          if (_hotKeywords.isNotEmpty) ...[
            _buildSectionHeader('热门搜索', Icons.trending_up_rounded),
            const SizedBox(height: 12),
            _buildChipGrid(
              _hotKeywords,
              isHot: true,
              onTap: _selectHistoryKeyword,
            ),
          ],

          // 搜索建议
          if (_searchHistory.isEmpty && _hotKeywords.isEmpty) ...[
            const SizedBox(height: 40),
            Center(
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.search_rounded,
                      size: 40,
                      color: Colors.blue.shade300,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '发现精彩内容',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '搜索动态、用户和钓点',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon,
      {VoidCallback? onClear}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey.shade700,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        const Spacer(),
        if (onClear != null)
          TextButton(
            onPressed: onClear,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              '清除',
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildChipGrid(
    List<String> items, {
    bool isHot = false,
    required Function(String) onTap,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: items.map((item) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => onTap(item),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
              decoration: BoxDecoration(
                color: isHot ? Colors.red.shade50 : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isHot ? Colors.red.shade100 : Colors.grey.shade200,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isHot) ...[
                    Icon(
                      Icons.local_fire_department_rounded,
                      size: 14,
                      color: Colors.red.shade400,
                    ),
                    const SizedBox(width: 4),
                  ],
                  Text(
                    item,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: isHot ? Colors.red.shade700 : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      itemBuilder: (context, index) => _buildSkeletonItem(),
    );
  }

  Widget _buildSkeletonItem() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 120,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(height: 6),
          Container(
            width: 200,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 40,
              color: Colors.grey.shade400,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '未找到相关内容',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '试试调整搜索词或筛选条件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSearchResults() {
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            _searchResults.length < _totalCount &&
            !_isLoading) {
          _loadMoreResults();
        }
        return false;
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _searchResults.length + (_isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _searchResults.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final result = _searchResults[index];
          return _buildModernSearchResultItem(result, index);
        },
      ),
    );
  }

  Widget _buildModernSearchResultItem(SearchResultVO result, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _navigateToDetail(result),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    _buildModernResultIcon(result.type),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            result.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              height: 1.3,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (result.authorName != null) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.person_outline_rounded,
                                  size: 14,
                                  color: Colors.grey.shade500,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  result.authorName!,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (result.location != null) ...[
                                  const SizedBox(width: 12),
                                  Icon(
                                    Icons.location_on_outlined,
                                    size: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                  const SizedBox(width: 4),
                                  Flexible(
                                    child: Text(
                                      result.location!,
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey.shade600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),

                // 内容
                if (result.content.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Text(
                    result.content,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // 底部操作栏
                if (result.type == 'moment') ...[
                  const SizedBox(height: 12),
                  _buildModernResultActions(result),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernResultIcon(String type) {
    IconData icon;
    Color color;

    switch (type) {
      case 'moment':
        icon = Icons.article_rounded;
        color = Colors.blue.shade500;
        break;
      case 'user':
        icon = Icons.person_rounded;
        color = Colors.green.shade500;
        break;
      case 'spot':
        icon = Icons.location_on_rounded;
        color = Colors.red.shade500;
        break;
      default:
        icon = Icons.search_rounded;
        color = Colors.grey.shade500;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Widget _buildModernResultActions(SearchResultVO result) {
    return Row(
      children: [
        if (result.likeCount != null)
          _buildActionButton(
            icon: Icons.favorite_rounded,
            count: result.likeCount!,
            color: Colors.red.shade400,
          ),
        if (result.commentCount != null) ...[
          const SizedBox(width: 16),
          _buildActionButton(
            icon: Icons.chat_bubble_rounded,
            count: result.commentCount!,
            color: Colors.grey.shade500,
          ),
        ],
        const Spacer(),
        IconButton(
          onPressed: () {
            // TODO: 分享功能
          },
          icon: Icon(
            Icons.share_rounded,
            size: 18,
            color: Colors.grey.shade500,
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required int count,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 13,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  IconData _getSuggestionIcon(String type) {
    switch (type) {
      case 'user':
        return Icons.person_rounded;
      case 'spot':
        return Icons.location_on_rounded;
      case 'keyword':
        return Icons.search_rounded;
      default:
        return Icons.lightbulb_outline_rounded;
    }
  }

  Color _getSuggestionColor(String type) {
    switch (type) {
      case 'user':
        return Colors.green.shade500;
      case 'spot':
        return Colors.red.shade500;
      case 'keyword':
        return Colors.blue.shade500;
      default:
        return Colors.orange.shade500;
    }
  }

  void _navigateToDetail(SearchResultVO result) {
    // TODO: 根据不同类型跳转到对应详情页
    switch (result.type) {
      case 'moment':
        // Navigator.push(context, MaterialPageRoute(builder: (context) => MomentDetailPage(momentId: result.id)));
        break;
      case 'user':
        // Navigator.push(context, MaterialPageRoute(builder: (context) => UserProfilePage(userId: result.id)));
        break;
      case 'spot':
        // Navigator.push(context, MaterialPageRoute(builder: (context) => FishingSpotDetailPage(spotId: result.id)));
        break;
    }
  }
}
