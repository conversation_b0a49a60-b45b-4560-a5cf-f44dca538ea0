import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/utils/moment_type_data_parser.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/utils/date_time_util.dart';

typedef MomentBuilder = Widget Function(BuildContext context, MomentVo moment);
typedef OnMomentTap = void Function(MomentVo moment);
typedef OnUserTap = void Function(dynamic user);
typedef OnLikeTap = void Function(int momentId);
typedef OnReportTap = void Function(MomentVo moment);

class MomentsList extends StatefulWidget {
  final List<MomentVo> moments;
  final bool isLoading;
  final bool hasMore;
  final ScrollController? scrollController;
  final Future<void> Function() onRefresh;
  final VoidCallback? onLoadMore;

  // 自定义回调
  final OnMomentTap? onMomentTap;
  final OnUserTap? onUserTap;
  final OnLikeTap? onLikeTap;
  final OnReportTap? onReportTap;

  // 自定义构建器（如果需要完全自定义卡片）
  final MomentBuilder? momentBuilder;

  // 显示选项
  final bool showSpotInfo;
  final bool showFloatingActionButton;
  final Widget? emptyWidget;
  final EdgeInsets padding;

  const MomentsList({
    super.key,
    required this.moments,
    required this.isLoading,
    required this.hasMore,
    required this.onRefresh,
    this.scrollController,
    this.onLoadMore,
    this.onMomentTap,
    this.onUserTap,
    this.onLikeTap,
    this.onReportTap,
    this.momentBuilder,
    this.showSpotInfo = true,
    this.showFloatingActionButton = false,
    this.emptyWidget,
    this.padding = const EdgeInsets.fromLTRB(16, 12, 16, 16),
  });

  @override
  State<MomentsList> createState() => _MomentsListState();
}

class _MomentsListState extends State<MomentsList> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    if (widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 400) {
      if (!widget.isLoading && widget.hasMore && widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading && widget.moments.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (widget.moments.isEmpty) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: widget.onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: widget.padding,
        itemCount: widget.moments.length +
            (widget.isLoading && widget.moments.isNotEmpty ? 1 : 0) +
            (!widget.hasMore && widget.moments.isNotEmpty ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < widget.moments.length) {
            final moment = widget.moments[index];
            return widget.momentBuilder?.call(context, moment) ??
                _buildDefaultMomentCard(moment);
          } else if (widget.isLoading && widget.moments.isNotEmpty) {
            return _buildLoadingIndicator();
          } else {
            return _buildNoMoreDataIndicator();
          }
        },
      ),
    );
  }

  Widget _buildDefaultEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                Icons.article_outlined,
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无动态数据',
              style: TextStyle(
                color: Colors.grey.shade800,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '还没有人分享动态',
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultMomentCard(MomentVo moment) {
    return GestureDetector(
      onTap: () {
        if (widget.onMomentTap != null) {
          widget.onMomentTap!(moment);
        } else {
          _defaultMomentTap(moment);
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息栏
            _buildUserHeader(moment),

            // 动态内容（类型特定内容 + 文字 + 图片）
            if (moment.typeSpecificData != null)
              _buildMomentContentWithType(moment)
            else if (moment.content != null || moment.images != null)
              _buildSimpleMomentContent(moment),

            // 互动栏
            _buildInteractionBar(moment),
          ],
        ),
      ),
    );
  }

  // 构建用户头部信息
  Widget _buildUserHeader(MomentVo moment) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              if (widget.onUserTap != null && moment.publisher != null) {
                widget.onUserTap!(moment.publisher);
              }
            },
            child: CircleAvatar(
              radius: 20,
              backgroundImage: moment.publisher?.avatarUrl != null
                  ? CachedNetworkImageProvider(moment.publisher!.avatarUrl!)
                  : null,
              child: moment.publisher?.avatarUrl == null
                  ? const Icon(Icons.person, size: 20)
                  : null,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  moment.publisher?.name ?? '匿名用户',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
                if (widget.showSpotInfo && moment.fishingSpotName != null)
                  Text(
                    moment.fishingSpotName!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
          Text(
            _formatTimeAgo(moment.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  // 构建带类型的动态内容
  Widget _buildMomentContentWithType(MomentVo moment) {
    final Color primaryColor;
    final Color backgroundColor;
    final IconData iconData;
    final String typeName;

    // 根据类型设置颜色和图标
    switch (moment.momentType) {
      case 'fishing_catch':
        primaryColor = Colors.blue.shade600;
        backgroundColor = Colors.blue.shade50;
        iconData = Icons.catching_pokemon;
        typeName = '钓获分享';
        break;
      case 'equipment':
        primaryColor = Colors.orange.shade600;
        backgroundColor = Colors.orange.shade50;
        iconData = Icons.shopping_bag;
        typeName = '装备展示';
        break;
      case 'technique':
        primaryColor = Colors.green.shade600;
        backgroundColor = Colors.green.shade50;
        iconData = Icons.lightbulb;
        typeName = '技巧分享';
        break;
      case 'question':
        primaryColor = Colors.purple.shade600;
        backgroundColor = Colors.purple.shade50;
        iconData = Icons.help_outline;
        typeName = '问答求助';
        break;
      default:
        primaryColor = Colors.grey.shade600;
        backgroundColor = Colors.grey.shade50;
        iconData = Icons.article;
        typeName = '动态分享';
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: primaryColor.withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 类型标题栏
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor.withOpacity(0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    iconData,
                    size: 16,
                    color: primaryColor,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  typeName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // 类型特定内容
          Padding(
            padding: const EdgeInsets.all(12),
            child: _buildTypeSpecificData(moment),
          ),

          // 分隔线
          if (moment.content != null && moment.content!.isNotEmpty)
            Divider(
              height: 1,
              thickness: 1,
              color: primaryColor.withOpacity(0.1),
              indent: 12,
              endIndent: 12,
            ),

          // 文字内容
          if (moment.content != null && moment.content!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                moment.content!,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.4,
                  color: Colors.black87,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),

          // 图片内容
          if (moment.images != null && moment.images!.isNotEmpty) ...[
            if (moment.content == null || moment.content!.isEmpty)
              const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildImageGrid(moment.images!),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 构建简单动态内容（无类型特定数据）
  Widget _buildSimpleMomentContent(MomentVo moment) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (moment.content != null && moment.content!.isNotEmpty)
            Text(
              moment.content!,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
                color: Colors.black87,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          if (moment.content != null &&
              moment.content!.isNotEmpty &&
              moment.images != null &&
              moment.images!.isNotEmpty)
            const SizedBox(height: 12),
          if (moment.images != null && moment.images!.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImageGrid(moment.images!),
            ),
        ],
      ),
    );
  }

  // 构建类型特定数据
  Widget _buildTypeSpecificData(MomentVo moment) {
    switch (moment.momentType) {
      case 'fishing_catch':
        return _buildFishingCatchData(moment);
      case 'equipment':
        return _buildEquipmentData(moment);
      case 'technique':
        return _buildTechniqueData(moment);
      case 'question':
        return _buildQuestionData(moment);
      default:
        return const SizedBox.shrink();
    }
  }

  // 构建钓获分享数据
  Widget _buildFishingCatchData(MomentVo moment) {
    final catchData =
        MomentTypeDataParser.parseFishingCatch(moment.typeSpecificData);
    if (catchData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 总重量
        if (catchData.totalWeight != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.fitness_center,
                  size: 14,
                  color: Colors.white,
                ),
                const SizedBox(width: 4),
                Text(
                  '总重量 ${catchData.totalWeight}kg',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

        // 渔获列表
        if (catchData.caughtFishes != null &&
            catchData.caughtFishes!.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: catchData.caughtFishes!.take(3).map((fish) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.shade200,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.set_meal,
                      size: 12,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${fish.fishTypeName ?? "未知"} x${fish.count ?? 1}',
                      style: const TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (fish.weight != null) ...[
                      const SizedBox(width: 4),
                      Text(
                        '(${fish.weight}kg)',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
          if (catchData.caughtFishes!.length > 3) ...[
            const SizedBox(width: 4),
            Text(
              '等${catchData.caughtFishes!.length}种',
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],

        // 钓法和天气
        if (catchData.fishingMethod != null ||
            catchData.weatherConditions != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              if (catchData.fishingMethod != null) ...[
                Icon(
                  Icons.phishing,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  catchData.fishingMethod!,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
              if (catchData.fishingMethod != null &&
                  catchData.weatherConditions != null)
                const SizedBox(width: 12),
              if (catchData.weatherConditions != null) ...[
                Icon(
                  Icons.wb_sunny_outlined,
                  size: 12,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  catchData.weatherConditions!,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  // 构建装备展示数据
  Widget _buildEquipmentData(MomentVo moment) {
    final equipmentData =
        MomentTypeDataParser.parseEquipment(moment.typeSpecificData);
    if (equipmentData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 装备名称和评分
        Row(
          children: [
            Expanded(
              child: Text(
                equipmentData.equipmentName ?? '装备',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (equipmentData.rating != null)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(5, (index) {
                  return Icon(
                    index < equipmentData.rating!
                        ? Icons.star
                        : Icons.star_border,
                    size: 12,
                    color: Colors.amber.shade600,
                  );
                }),
              ),
          ],
        ),

        // 装备信息
        const SizedBox(height: 8),
        Wrap(
          spacing: 12,
          runSpacing: 4,
          children: [
            if (equipmentData.category != null)
              _buildEquipmentInfo(Icons.category, equipmentData.category!),
            if (equipmentData.brand != null)
              _buildEquipmentInfo(Icons.business, equipmentData.brand!),
            if (equipmentData.model != null)
              _buildEquipmentInfo(Icons.model_training, equipmentData.model!),
            if (equipmentData.price != null)
              _buildEquipmentInfo(Icons.attach_money, equipmentData.price!),
          ],
        ),

        // 适用鱼种
        if (equipmentData.targetFishTypes != null &&
            equipmentData.targetFishTypes!.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: equipmentData.targetFishTypes!.take(3).map((fish) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.shade200,
                    width: 1,
                  ),
                ),
                child: Text(
                  fish.name ?? "未知",
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.orange.shade700,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  // 构建技巧分享数据
  Widget _buildTechniqueData(MomentVo moment) {
    final techniqueData =
        MomentTypeDataParser.parseTechnique(moment.typeSpecificData);
    if (techniqueData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 技巧名称和难度
        Row(
          children: [
            Expanded(
              child: Text(
                techniqueData.techniqueName ?? '技巧',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (techniqueData.difficulty != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(techniqueData.difficulty!),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  techniqueData.difficulty!,
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),

        // 技巧描述
        if (techniqueData.description != null) ...[
          const SizedBox(height: 6),
          Text(
            techniqueData.description!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],

        // 环境和鱼种
        const SizedBox(height: 6),
        Row(
          children: [
            if (techniqueData.environments != null &&
                techniqueData.environments!.isNotEmpty) ...[
              Icon(
                Icons.landscape,
                size: 12,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  techniqueData.environments!.join('、'),
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  // 构建问答求助数据
  Widget _buildQuestionData(MomentVo moment) {
    final questionData =
        MomentTypeDataParser.parseQuestion(moment.typeSpecificData);
    if (questionData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题标题
        Text(
          questionData.questionTitle ?? '问题',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // 问题详情
        if (questionData.detailedProblem != null) ...[
          const SizedBox(height: 6),
          Text(
            questionData.detailedProblem!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],

        // 标签
        if (questionData.tags != null && questionData.tags!.isNotEmpty) ...[
          const SizedBox(height: 6),
          Wrap(
            spacing: 4,
            children: questionData.tags!.take(3).map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.purple.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.purple.shade200,
                    width: 1,
                  ),
                ),
                child: Text(
                  tag,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.purple.shade700,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  // 构建互动栏
  Widget _buildInteractionBar(MomentVo moment) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
      child: Row(
        children: [
          _buildInteractionButton(
            icon: (moment.isLiked ?? false)
                ? Icons.thumb_up
                : Icons.thumb_up_outlined,
            count: moment.likeCount,
            color: (moment.isLiked ?? false)
                ? Theme.of(context).primaryColor
                : null,
            onTap: widget.onLikeTap != null && moment.id != null
                ? () => widget.onLikeTap!(moment.id!)
                : null,
          ),
          const SizedBox(width: 16),
          _buildInteractionButton(
            icon: Icons.chat_bubble_outline,
            count: moment.commentCount,
            onTap: () => widget.onMomentTap != null
                ? widget.onMomentTap!(moment)
                : _defaultMomentTap(moment),
          ),
          const Spacer(),
          if (widget.showSpotInfo && moment.fishingSpotName != null)
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.location_on,
                  size: 14,
                  color: Colors.grey.shade500,
                ),
                const SizedBox(width: 2),
                Text(
                  moment.fishingSpotName!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
        ],
      ),
    );
  }

  // 辅助方法：构建装备信息项
  Widget _buildEquipmentInfo(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildImageGrid(List<dynamic> images) {
    final displayImages = images.take(3).toList();
    final remainingCount = images.length - 3;

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final imageSize = (width - 8) / 3;

        if (images.length == 1) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedNetworkImage(
              imageUrl: images[0].imageUrl ?? images[0],
              width: width,
              height: width * 0.6,
              fit: BoxFit.cover,
            ),
          );
        }

        return Row(
          children: displayImages.asMap().entries.map((entry) {
            final index = entry.key;
            final image = entry.value;
            final isLast = index == displayImages.length - 1;

            return Padding(
              padding: EdgeInsets.only(
                  right: index < displayImages.length - 1 ? 4 : 0),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: CachedNetworkImage(
                      imageUrl: image.imageUrl ?? image,
                      width: imageSize,
                      height: imageSize,
                      fit: BoxFit.cover,
                    ),
                  ),
                  if (isLast && remainingCount > 0)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Center(
                          child: Text(
                            '+$remainingCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    int? count,
    Color? color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 20, color: color ?? Colors.grey.shade600),
            if (count != null && count > 0) ...[
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: color ?? Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildNoMoreDataIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          '没有更多动态了',
          style: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  void _defaultMomentTap(MomentVo moment) async {
    final result = await Navigator.of(context).push<MomentVo>(
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(
          momentId: moment.id!,
          initialMoment: moment,
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return Colors.green;
      case '进阶级':
        return Colors.orange;
      case '专家级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getMomentTypeColor(String type) {
    switch (type) {
      case 'fishing_catch':
        return Colors.blue.shade600;
      case 'equipment':
        return Colors.orange.shade600;
      case 'technique':
        return Colors.green.shade600;
      case 'question':
        return Colors.purple.shade600;
      default:
        return Colors.grey;
    }
  }

  IconData _getMomentTypeIcon(String type) {
    switch (type) {
      case 'fishing_catch':
        return Icons.catching_pokemon;
      case 'equipment':
        return Icons.shopping_bag;
      case 'technique':
        return Icons.lightbulb;
      case 'question':
        return Icons.help;
      default:
        return Icons.article;
    }
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';

    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }
}
