import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/providers/user_profile_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/location_selection_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/map_state_provider.dart';
import 'package:user_app/features/fishing_spots/view_models/publish_moment_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/tips/view_models/tips_view_model.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/services/oss_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/view_models/chat_view_model.dart';
import 'package:user_app/view_models/preview_image_view_model.dart';

List<SingleChildWidget> providers() {
  return [
    Provider(
      create: (_) => getIt<MomentService>(),
    ),
    Provider(
      create: (_) => getIt<OssService>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<PreviewImageViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<ChatViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<AuthViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<LoginViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<RegisterViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<ResetPasswordViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<UserProfileViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<FishingSpotViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<WeatherCardViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<PublishMomentViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<CommunityViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<TipsViewModel>(),
    ),
    ChangeNotifierProvider(
      create: (_) => getIt<MapStateViewModel>(),
    ),
    ChangeNotifierProvider(create: (_) => getIt<LocationSelectionViewModel>()),
    ChangeNotifierProvider(create: (_) => getIt<CommentViewModel>()),
  ];
}
