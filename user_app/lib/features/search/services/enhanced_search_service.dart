import 'package:flutter/foundation.dart';
import 'package:user_app/api/search_api.dart';
import 'package:user_app/models/search/search_models.dart';
import 'dart:async';

/// 增强的搜索服务
///
/// 基于后端MySQL的智能搜索服务：
/// 1. 实时搜索建议
/// 2. 搜索历史管理
/// 3. 热门搜索展示
/// 4. 防抖和缓存优化
class EnhancedSearchService {
  final SearchApi _searchApi;

  // 搜索缓存
  final Map<String, SearchPage<SearchResultVO>> _searchCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // 防抖Timer
  Timer? _debounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 500);

  EnhancedSearchService(this._searchApi);

  /// 综合搜索
  Future<SearchPage<SearchResultVO>> search(
    String keyword, {
    String type = 'all',
    String sortBy = 'relevance',
    int page = 1,
    int size = 10,
    String? province,
    String? city,
    int? dayRange,
  }) async {
    if (keyword.trim().isEmpty) {
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }

    // 构建搜索请求
    final request = SearchRequest(
      keyword: keyword,
      type: type,
      sortBy: sortBy,
      page: page,
      size: size,
      province: province,
      city: city,
      dayRange: dayRange,
    );

    // 检查缓存
    final cacheKey = _buildCacheKey(request);
    if (_isCacheValid(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    try {
      // 记录搜索行为
      if (page == 1) {
        _recordSearchAsync(keyword);
      }

      final response = await _searchApi.search(request);

      if (response.isSuccess && response.data != null) {
        // 缓存结果
        _searchCache[cacheKey] = response.data!;
        _cacheTimestamps[cacheKey] = DateTime.now();

        return response.data!;
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      debugPrint('搜索失败: $e');
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }
  }

  /// 搜索建议（带防抖）
  Future<List<SearchSuggestionVO>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    // 移除前端的长度限制，让后端处理所有情况
    // 现在后端会在关键词为空或很短时返回默认建议

    final completer = Completer<List<SearchSuggestionVO>>();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final response =
            await _searchApi.getSearchSuggestions(keyword, limit: limit);

        if (response.isSuccess && response.data != null) {
          completer.complete(response.data!);
        } else {
          // 如果API调用失败，返回默认建议
          completer.complete(_getDefaultSuggestions(limit));
        }
      } catch (e) {
        debugPrint('获取搜索建议失败: $e');
        // 发生异常时也返回默认建议，而不是空列表
        completer.complete(_getDefaultSuggestions(limit));
      }
    });

    return completer.future;
  }

  /// 获取默认搜索建议（前端备用）
  List<SearchSuggestionVO> _getDefaultSuggestions(int limit) {
    final defaultSuggestions = [
      SearchSuggestionVO(
        suggestion: '钓鱼',
        type: 'keyword',
      ),
      SearchSuggestionVO(
        suggestion: '钓点',
        type: 'keyword',
      ),
      SearchSuggestionVO(
        suggestion: '分享',
        type: 'keyword',
      ),
      SearchSuggestionVO(
        suggestion: '技巧',
        type: 'keyword',
      ),
      SearchSuggestionVO(
        suggestion: '装备',
        type: 'keyword',
      ),
    ];

    return defaultSuggestions.take(limit).toList();
  }

  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    try {
      final response = await _searchApi.getSearchHistory();

      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      return [];
    } catch (e) {
      debugPrint('获取搜索历史失败: $e');
      return [];
    }
  }

  /// 清除搜索历史
  Future<bool> clearSearchHistory() async {
    try {
      final response = await _searchApi.clearSearchHistory();
      return response.isSuccess;
    } catch (e) {
      debugPrint('清除搜索历史失败: $e');
      return false;
    }
  }

  /// 获取热门搜索
  Future<List<String>> getHotSearchKeywords({int limit = 10}) async {
    try {
      final response = await _searchApi.getHotSearchKeywords(limit: limit);

      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      return [];
    } catch (e) {
      debugPrint('获取热门搜索失败: $e');
      return [];
    }
  }

  /// 异步记录搜索行为
  void _recordSearchAsync(String keyword) {
    _searchApi.recordSearch(keyword).then((_) {
      // 记录成功
    }).catchError((e) {
      debugPrint('记录搜索行为失败: $e');
    });
  }

  /// 构建缓存键
  String _buildCacheKey(SearchRequest request) {
    return '${request.keyword}_${request.type}_${request.sortBy}_${request.page}_${request.size}_${request.province}_${request.city}_${request.dayRange}';
  }

  /// 检查缓存是否有效
  bool _isCacheValid(String cacheKey) {
    if (!_searchCache.containsKey(cacheKey) ||
        !_cacheTimestamps.containsKey(cacheKey)) {
      return false;
    }

    final cacheTime = _cacheTimestamps[cacheKey]!;
    return DateTime.now().difference(cacheTime) < _cacheExpiry;
  }

  /// 清除搜索缓存
  void clearCache() {
    _searchCache.clear();
    _cacheTimestamps.clear();
  }

  /// 销毁资源
  void dispose() {
    _debounceTimer?.cancel();
    _searchCache.clear();
    _cacheTimestamps.clear();
  }
}
