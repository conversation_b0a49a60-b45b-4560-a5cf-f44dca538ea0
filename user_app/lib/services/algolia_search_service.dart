import 'package:flutter/foundation.dart';
import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:user_app/models/search/search_models.dart';
import 'package:user_app/config/algolia_config.dart';
import 'package:user_app/core/di/injection.dart';
import 'dart:async';

/// Algolia 搜索服务
///
/// 基于 Algolia 的高性能搜索服务：
/// 1. 实时搜索建议
/// 2. 分面搜索和过滤
/// 3. 高亮显示
/// 4. 地理位置搜索
/// 5. 个性化搜索
class AlgoliaSearchService {
  late final SearchClient _client;

  // 搜索历史和热门关键词（本地存储）
  final List<String> _searchHistory = [];
  final List<String> _hotKeywords = [];

  // 防抖Timer
  Timer? _debounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 300);

  AlgoliaSearchService() {
    _initializeAlgolia();
  }

  void _initializeAlgolia() {
    // 从依赖注入容器获取已配置的 SearchClient
    _client = getIt<SearchClient>();
  }

  /// 综合搜索
  Future<SearchPage<SearchResultVO>> search(
    String keyword, {
    String type = 'all',
    String sortBy = 'relevance',
    int page = 1,
    int size = 20,
    String? province,
    String? city,
    int? dayRange,
  }) async {
    if (keyword.trim().isEmpty) {
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }

    try {
      // 记录搜索历史
      if (page == 1) {
        await addToSearchHistory(keyword);
      }

      List<SearchResultVO> allResults = [];
      int totalCount = 0;

      if (type == 'all') {
        // 并行搜索所有类型
        final futures = await Future.wait([
          _searchInIndex(AlgoliaConfig.momentsIndex, keyword, 'moment', page,
              size, province, city, dayRange),
          _searchInIndex(AlgoliaConfig.usersIndex, keyword, 'user', page, size,
              province, city, dayRange),
          _searchInIndex(AlgoliaConfig.fishingSpotsIndex, keyword, 'spot', page,
              size, province, city, dayRange),
        ]);

        // 合并结果
        for (final results in futures) {
          allResults.addAll(results);
        }

        // 按相关性排序
        allResults.sort(
            (a, b) => (b.relevanceScore ?? 0).compareTo(a.relevanceScore ?? 0));

        // 分页处理
        final startIndex = (page - 1) * size;
        final endIndex = startIndex + size;
        final paginatedResults = allResults.length > startIndex
            ? allResults.sublist(
                startIndex, endIndex.clamp(0, allResults.length))
            : <SearchResultVO>[];

        totalCount = allResults.length;
        allResults = paginatedResults;
      } else {
        // 单类型搜索
        String indexName;
        switch (type) {
          case 'moment':
            indexName = AlgoliaConfig.momentsIndex;
            break;
          case 'user':
            indexName = AlgoliaConfig.usersIndex;
            break;
          case 'spot':
            indexName = AlgoliaConfig.fishingSpotsIndex;
            break;
          default:
            indexName = AlgoliaConfig.momentsIndex;
        }

        allResults = await _searchInIndex(
            indexName, keyword, type, page, size, province, city, dayRange);
        totalCount = allResults.length;
      }

      return SearchPage<SearchResultVO>(
        records: allResults,
        total: totalCount,
        size: size,
        current: page,
      );
    } catch (e) {
      debugPrint('Algolia 搜索失败: $e');
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }
  }

  /// 在指定索引中搜索
  Future<List<SearchResultVO>> _searchInIndex(
    String indexName,
    String keyword,
    String type,
    int page,
    int size,
    String? province,
    String? city,
    int? dayRange,
  ) async {
    try {
      // 构建过滤条件
      final filters = <String>[];

      if (province != null && province.isNotEmpty) {
        filters.add('province:"$province"');
      }

      if (city != null && city.isNotEmpty) {
        filters.add('city:"$city"');
      }

      if (dayRange != null && dayRange > 0) {
        final timestamp = DateTime.now()
                .subtract(Duration(days: dayRange))
                .millisecondsSinceEpoch ~/
            1000;
        filters.add('createdAt > $timestamp');
      }

      // 创建搜索请求
      final searchRequest = SearchForHits(
        indexName: indexName,
        query: keyword,
        hitsPerPage: size,
        page: page - 1, // Algolia 使用 0 基索引
        attributesToRetrieve: AlgoliaConfig.attributesToRetrieve,
        attributesToHighlight: AlgoliaConfig.attributesToHighlight,
        highlightPreTag: AlgoliaConfig.highlightPreTag,
        highlightPostTag: AlgoliaConfig.highlightPostTag,
        facetFilters: filters.isNotEmpty ? [filters] : null,
      );

      // 执行搜索
      final response = await _client.searchIndex(request: searchRequest);

      // 转换结果
      return response.hits
          .map((hit) => _convertHitToSearchResult(hit, type))
          .toList();
    } catch (e) {
      debugPrint('搜索索引 $indexName 失败: $e');
      return [];
    }
  }

  /// 将 Algolia Hit 转换为 SearchResultVO
  SearchResultVO _convertHitToSearchResult(
      Map<String, dynamic> hit, String type) {
    return SearchResultVO(
      id: _parseId(hit['objectID']),
      type: type,
      title: _extractTitle(hit, type),
      content: hit['content'] ?? hit['description'] ?? hit['bio'] ?? '',
      authorName: hit['authorName'] ?? hit['name'],
      authorAvatar: hit['authorAvatar'] ?? hit['avatarUrl'],
      location: hit['location'] ?? hit['address'],
      createTime: _parseDateTime(hit['createdAt']),
      likeCount: hit['likeCount'],
      commentCount: hit['commentCount'],
      relevanceScore: 1.0, // Algolia 会自动计算相关性
      highlightContent: _extractHighlight(hit['_highlightResult']),
    );
  }

  /// 提取标题
  String _extractTitle(Map<String, dynamic> hit, String type) {
    switch (type) {
      case 'moment':
        final content = hit['content'] ?? '';
        return content.length > 50 ? '${content.substring(0, 50)}...' : content;
      case 'user':
        return hit['name'] ?? '';
      case 'spot':
        return hit['name'] ?? '';
      default:
        return hit['title'] ?? hit['name'] ?? '';
    }
  }

  /// 解析 ID
  int _parseId(dynamic objectId) {
    if (objectId is int) return objectId;
    if (objectId is String) return int.tryParse(objectId) ?? 0;
    return 0;
  }

  /// 解析日期时间
  DateTime? _parseDateTime(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }
    if (timestamp is String) {
      return DateTime.tryParse(timestamp);
    }
    return null;
  }

  /// 提取高亮内容
  String? _extractHighlight(Map<String, dynamic>? highlightResult) {
    if (highlightResult == null) return null;

    for (final key in ['title', 'content', 'name', 'description']) {
      final highlight = highlightResult[key];
      if (highlight != null && highlight['value'] != null) {
        return highlight['value'];
      }
    }
    return null;
  }

  /// 搜索建议（带防抖）
  Future<List<SearchSuggestionVO>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    if (keyword.trim().isEmpty) {
      return _getDefaultSuggestions(limit);
    }

    final completer = Completer<List<SearchSuggestionVO>>();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final suggestions = <SearchSuggestionVO>[];

        // 从不同索引获取建议
        final futures = await Future.wait([
          _getSuggestionsFromIndex(
              AlgoliaConfig.momentsIndex, keyword, 'moment'),
          _getSuggestionsFromIndex(AlgoliaConfig.usersIndex, keyword, 'user'),
          _getSuggestionsFromIndex(
              AlgoliaConfig.fishingSpotsIndex, keyword, 'spot'),
        ]);

        for (final indexSuggestions in futures) {
          suggestions.addAll(indexSuggestions);
        }

        // 按相关性排序并限制数量
        suggestions.sort((a, b) => (b.count ?? 0).compareTo(a.count ?? 0));
        final limitedSuggestions = suggestions.take(limit).toList();

        completer.complete(limitedSuggestions);
      } catch (e) {
        debugPrint('获取 Algolia 搜索建议失败: $e');
        completer.complete(_getDefaultSuggestions(limit));
      }
    });

    return completer.future;
  }

  /// 从索引获取搜索建议
  Future<List<SearchSuggestionVO>> _getSuggestionsFromIndex(
    String indexName,
    String keyword,
    String type,
  ) async {
    try {
      final searchRequest = SearchForHits(
        indexName: indexName,
        query: keyword,
        hitsPerPage: 5,
        attributesToRetrieve: ['name', 'title', 'content'],
      );

      final response = await _client.searchIndex(request: searchRequest);

      return response.hits
          .map((hit) {
            final suggestion =
                hit['name'] ?? hit['title'] ?? hit['content'] ?? '';
            return SearchSuggestionVO(
              suggestion: suggestion,
              type: type,
              count: response.nbHits,
            );
          })
          .where((s) => s.suggestion.isNotEmpty)
          .toList();
    } catch (e) {
      debugPrint('获取 $type 建议失败: $e');
      return [];
    }
  }

  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    // TODO: 从本地存储获取搜索历史
    return _searchHistory;
  }

  /// 添加到搜索历史
  Future<bool> addToSearchHistory(String keyword) async {
    if (keyword.trim().isEmpty) return false;

    // 移除重复项
    _searchHistory.remove(keyword);
    // 添加到开头
    _searchHistory.insert(0, keyword);
    // 限制历史记录数量
    if (_searchHistory.length > AlgoliaConfig.maxSearchHistory) {
      _searchHistory.removeRange(
          AlgoliaConfig.maxSearchHistory, _searchHistory.length);
    }

    // TODO: 保存到本地存储
    return true;
  }

  /// 清除搜索历史
  Future<bool> clearSearchHistory() async {
    _searchHistory.clear();
    // TODO: 清除本地存储
    return true;
  }

  /// 获取热门搜索关键词
  Future<List<String>> getHotKeywords() async {
    // TODO: 从后端API或本地缓存获取热门关键词
    return _hotKeywords.isNotEmpty
        ? _hotKeywords
        : [
            '鲫鱼',
            '黑坑',
            '野钓',
            '路亚',
            '海钓',
            '钓鲤鱼',
            '钓草鱼',
            '夜钓',
          ];
  }

  /// 获取默认搜索建议
  List<SearchSuggestionVO> _getDefaultSuggestions(int limit) {
    final defaultSuggestions = [
      SearchSuggestionVO(suggestion: '鲫鱼钓法', type: 'keyword'),
      SearchSuggestionVO(suggestion: '黑坑技巧', type: 'keyword'),
      SearchSuggestionVO(suggestion: '野钓装备', type: 'keyword'),
      SearchSuggestionVO(suggestion: '路亚入门', type: 'keyword'),
      SearchSuggestionVO(suggestion: '海钓攻略', type: 'keyword'),
    ];
    return defaultSuggestions.take(limit).toList();
  }

  /// 释放资源
  void dispose() {
    _debounceTimer?.cancel();
  }
}
