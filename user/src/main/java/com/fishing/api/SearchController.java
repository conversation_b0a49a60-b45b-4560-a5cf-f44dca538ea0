package com.fishing.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.config.CurrentUser;
import com.fishing.dto.search.SearchRequestDTO;
import com.fishing.service.SearchService;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.search.SearchResultVO;
import com.fishing.vo.search.SearchSuggestionVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 搜索接口
 */
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
@Tag(name = "搜索接口")
public class SearchController {

    private final SearchService searchService;

    @PostMapping
    @Operation(summary = "综合搜索")
    public ResponseEntity<ApiResponse<Page<SearchResultVO>>> search(
            @RequestBody @Valid SearchRequestDTO request,
            @CurrentUser Long userId) {
        Page<SearchResultVO> results = searchService.search(request, userId);
        return ResponseEntity.ok(ApiResponse.success(results));
    }

    @GetMapping("/suggestions")
    @Operation(summary = "获取搜索建议")
    public ResponseEntity<ApiResponse<List<SearchSuggestionVO>>> getSearchSuggestions(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit,
            @CurrentUser Long userId) {
        List<SearchSuggestionVO> suggestions = searchService.getSearchSuggestions(keyword, limit, userId);
        return ResponseEntity.ok(ApiResponse.success(suggestions));
    }

    @GetMapping("/history")
    @Operation(summary = "获取搜索历史")
    public ResponseEntity<ApiResponse<List<String>>> getSearchHistory(@CurrentUser Long userId) {
        List<String> history = searchService.getSearchHistory(userId);
        return ResponseEntity.ok(ApiResponse.success(history));
    }

    @DeleteMapping("/history")
    @Operation(summary = "清除搜索历史")
    public ResponseEntity<ApiResponse<Void>> clearSearchHistory(@CurrentUser Long userId) {
        searchService.clearSearchHistory(userId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @GetMapping("/hot")
    @Operation(summary = "获取热门搜索")
    public ResponseEntity<ApiResponse<List<String>>> getHotSearchKeywords(
            @RequestParam(defaultValue = "10") Integer limit) {
        List<String> hotKeywords = searchService.getHotSearchKeywords(limit);
        return ResponseEntity.ok(ApiResponse.success(hotKeywords));
    }

    @PostMapping("/record")
    @Operation(summary = "记录搜索行为")
    public ResponseEntity<ApiResponse<Void>> recordSearch(
            @RequestParam String keyword,
            @CurrentUser Long userId) {
        searchService.recordSearch(keyword, userId);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
